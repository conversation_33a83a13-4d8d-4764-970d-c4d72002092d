/**
 * 限制检查相关的类型定义
 */

/**
 * 限制检查选项
 */
export interface LimitCheckOptions {
  /** 是否启用限制检查 */
  enableLimitCheck?: boolean;
  /** 当超过限制时是否抛出错误 */
  throwOnLimitExceeded?: boolean;
  /** 是否在控制台输出详细日志 */
  verbose?: boolean;
}

/**
 * 限制检查结果
 */
export interface LimitCheckResult {
  /** 是否通过限制检查 */
  isValid: boolean;
  /** 最大限制值 */
  maxLimit: string;
  /** 检查结果说明 */
  reason: string;
  /** 原始金额 */
  originalAmount: string;
  /** 检查类型 */
  checkType: 'swap' | 'addLiquidity' | 'position';
}

/**
 * Swap 限制检查参数
 */
export interface SwapLimitCheckParams {
  coinInType: string;
  coinOutType: string;
  amountIn: string;
  inCoinDecimals: number;
}

/**
 * 添加流动性限制检查参数
 */
export interface AddLiquidityLimitCheckParams {
  coinType: string;
  assetAmount: string;
  assetDecimals: number;
}

/**
 * 开仓限制检查参数
 */
export interface PositionLimitCheckParams {
  payCoinType: string;
  collateralCoinType: string;
  amountIn: string;
  inCoinDecimals: number;
}

/**
 * 限制检查错误类型
 */
export enum LimitCheckErrorType {
  SWAP_LIMIT_EXCEEDED = 'SWAP_LIMIT_EXCEEDED',
  ADD_LIQUIDITY_LIMIT_EXCEEDED = 'ADD_LIQUIDITY_LIMIT_EXCEEDED',
  POSITION_LIMIT_EXCEEDED = 'POSITION_LIMIT_EXCEEDED',
  QUERY_FAILED = 'QUERY_FAILED',
  INVALID_PARAMS = 'INVALID_PARAMS',
}

/**
 * 限制检查错误
 */
export class LimitCheckError extends Error {
  public readonly errorType: LimitCheckErrorType;
  public readonly checkResult?: LimitCheckResult;

  constructor(
    message: string,
    errorType: LimitCheckErrorType,
    checkResult?: LimitCheckResult,
  ) {
    super(message);
    this.name = 'LimitCheckError';
    this.errorType = errorType;
    this.checkResult = checkResult;
  }
}

/**
 * 默认限制检查配置
 */
export const DEFAULT_LIMIT_CHECK_OPTIONS: Required<LimitCheckOptions> = {
  enableLimitCheck: true,
  throwOnLimitExceeded: true,
  verbose: false,
};

/**
 * 限制检查配置
 */
export const LIMIT_CHECK_CONFIG = {
  SWAP: {
    SAFETY_MARGIN: 0.8,
    MIN_TEST_AMOUNT_THRESHOLD: 0.00001,
  },
  ADD_LIQUIDITY: {
    SAFETY_MARGIN: 0.9,
    MIN_TEST_AMOUNT_THRESHOLD: 0.0001,
  },
  POSITION: {
    SAFETY_MARGIN: 0.8,
    MIN_TEST_AMOUNT_THRESHOLD: 0.0001,
  },
} as const;
