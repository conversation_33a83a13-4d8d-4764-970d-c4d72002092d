/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  COMMON_CONSTS,
  CONTRACT_MODULE,
  CONTRACT_FUNCTION,
} from '../constants';
import { HertzFlowSDK } from '../sdk';
import { bcs } from '@mysten/bcs';
import { fromDecimalsAmount, toDecimalsAmount } from '../utils';
import { BigNumber } from 'bignumber.js';
import { SuiParsedData } from '@mysten/sui/client';
import { Transaction } from '@mysten/sui/transactions';
import { HertzflowError } from '../errors/errors';
import { PRICE_PRECISION_POWER, ZERO } from '../math';

/**
 * 合约内提供的查询接口
 */

// ===== Type Definitions =====

/**
 * 添加流动性查询参数
 */
export interface QueryAddLiquidityParams {
  /** 代币类型 */
  coinType: string;
  /** 输入金额 */
  amount: string;
  /** 代币精度 */
  decimals: number;
  /** 滑点容忍度 (小数形式，如 0.01 = 1%) */
  slippage: number;
}

/**
 * 移除流动性查询参数
 */
export interface QueryRemoveLiquidityParams {
  /** 代币类型 */
  coinType: string;
  /** HZLP代币数量 */
  hzlpAmount: string;
  /** 滑点容忍度 (小数形式，如 0.01 = 1%) */
  slippage: number;
}

/**
 * 流动性查询结果
 */
export interface LiquidityQueryResult {
  /** 输出金额 */
  amountOutAfterFee: string;
  /** 考虑滑点后的输出金额 */
  amountOutAfterFeeWithSlippage: string;
  /** 手续费(包括固定添加移除流动性fee和动态taxFee) */
  fee: string;
}

/**
 * Swap 查询参数
 */
export interface SwapWithAmountInQueryParams {
  typeArguments: [inCoin: string, outCoin: string];
  /** 期望输入代币数量 */
  expectedAmountIn: string;
  /** 输入代币精度 */
  inCoinDecimals: number;
  /** 滑点容忍度 (小数形式，如 0.01 = 1%) */
  slippage: number;
}

export interface SwapWithAmountOutQueryParams {
  typeArguments: [inCoin: string, outCoin: string];
  /** 期望输出金额 */
  expectedAmountOut: string;
  /** 输出代币精度 */
  outCoinDecimals: number;
  /** 滑点容忍度 (小数形式，如 0.01 = 1%) */
  slippage: number;
}

/**
 * Swap 输出查询结果
 */
export interface SwapAmountOutResult {
  /** 扣费后输出数量 */
  amountOutAfterSwap: string;
  /** 考虑滑点后的输出数量 */
  amountOutAfterSwapWithSlippage: string;
  /** 手续费用 */
  fee: string;
  /** 费率基点 */
  feeRateBps: string;
}

/**
 * Swap 输入查询结果
 */
export interface SwapAmountInResult {
  /** 扣费后输入数量 */
  amountInAfterSwap: string;
  /** 考虑滑点后的输入数量 */
  amountInAfterSwapWithSlippage: string;
  fee: string;
  /** 费率基点 */
  feeRateBps: string;
}

/**
 * 最大 Swap 输入查询参数
 */
export interface MaxSwapInParams {
  /** 输入代币类型 */
  coinInType: string;
  /** 输出代币类型 */
  coinOutType: string;
}

/**
 * 开仓仓位大小计算参数
 */
export interface CalculateOpenPositionSizeParams {
  /** 输入代币数量 */
  inputCoinAmount: number;
  /** 输入代币价格 */
  inputCoinPrice: number;
  /** 杠杆倍数 */
  leverage: number;
}

/**
 * 开仓仓位大小计算结果
 */
export interface CalculateOpenPositionSizeResult {
  /** 仓位大小 (USD) */
  sizeUsd: string;
  /** 扣费后的保证金 (USD) */
  collateralAfterFee: string;
}

/**
 * 费用计算参数
 */
export interface CalculateFeeParams {
  /** 输入金额 */
  amount: string;
  /** 费率 (基点) */
  feeBps: string;
}

/**
 * 费用计算结果
 */
export interface CalculateFeeResult {
  /** 扣费后金额 */
  afterFeeAmount: string;
  /** 费用金额 */
  feeAmount: string;
}

/**
 * 仓位费用计算参数
 */
export interface CalculatePositionFeeParams {
  /** 仓位大小变化 */
  sizeDelta: string;
  /** 费率 (基点) */
  feeBps: string;
}

/**
 * 仓位费用计算结果
 */
export interface CalculatePositionFeeResult {
  /** 扣费后USD */
  afterFeeUsd: string;
  /** 费用USD */
  feeUsd: string;
}

/**
 * 原始金额计算参数
 */
export interface CalculateOriginalAmountParams {
  /** 扣费后金额 */
  afterFeeAmount: string;
  /** 费率 (基点) */
  feeBps: string;
}

export interface HzlpObjectInfo {
  type: string;
  decimals: number;
  description: string;
  id: { id: string };
  name: string;
  symbol: string;
}

/**
 * ACL 权限配置
 */
export interface ACLConfig {
  type: string;
  fields: {
    permissions: {
      type: string;
      fields: {
        head: string;
        id: { id: string };
        size: string;
        tail: string;
      };
    };
  };
}

/**
 * 功能控制配置
 */
export interface FeatureConfig {
  type: string;
  fields: {
    disabled_flags: {
      type: string;
      fields: {
        id: { id: string };
        size: string;
      };
    };
  };
}

/**
 * 费用配置
 */
export interface FeeConfig {
  type: string;
  fields: {
    add_remove_fee_bps: string;
    decrease_position_bps: string;
    increase_position_bps: string;
    liquidation_fee_bps: string;
    protocol_fee_bps: string;
    stable_swap_fee_bps: string;
    stable_tax_bps: string;
    swap_fee_bps: string;
    tax_bps: string;
  };
}

/**
 * 资金费率配置
 */
export interface FundingFeeConfig {
  type: string;
  fields: {
    funding_interval: string;
    funding_rate_bps: string;
    stable_funding_rate_bps: string;
  };
}

/**
 * 白名单代币条目
 */
export interface WhiteListEntry {
  type: string;
  fields: {
    key: {
      type: string;
      fields: {
        name: string;
      };
    };
    value: number;
  };
}

/**
 * 白名单配置
 */
export interface WhiteListConfig {
  type: string;
  fields: {
    contents: WhiteListEntry[];
  };
}

/**
 * 费用类型枚举 - 对应 fee.move 中的 FeeKey
 */
export enum FeeKey {
  /** 清算费用 - 当仓位被清算时收取的费用 */
  LiquidationFee = 0,
  /** 税费 - 一般交易税费 */
  TaxFee = 1,
  /** 稳定币税费 - 稳定币相关交易的税费 */
  StableTaxFee = 2,
  /** 兑换费用 - 代币兑换时收取的费用 */
  SwapFee = 3,
  /** 稳定币兑换费用 - 稳定币兑换时收取的费用 */
  StableSwapFee = 4,
  /** 添加/移除流动性费用 - 添加或移除流动性时收取的费用 */
  AddRemoveFee = 5,
  /** 开仓费用 - 增加仓位时收取的费用 */
  IncreasePositionFee = 6,
  /** 平仓费用 - 减少仓位时收取的费用 */
  DecreasePositionFee = 7,
  /** 协议费用 - 协议收取的费用 */
  ProtocolFee = 8,
  /** 预留字段1 */
  Reserved1 = 9,
  /** 预留字段2 */
  Reserved2 = 10,
  /** 预留字段3 */
  Reserved3 = 11,
}

/**
 * 费用计算结果
 */
export interface FeeCalculationResult {
  /** 扣费后金额 */
  afterFeeAmount: string;
  /** 费用金额 */
  feeAmount: string;
}

/**
 * 仓位费用计算结果
 */
export interface PositionFeeCalculationResult {
  /** 扣费后USD金额 */
  afterFeeUsd: string;
  /** 费用USD金额 */
  feeUsd: string;
}

/**
 * ProtocolStore 对象信息
 */
export interface ProtocolStoreObjectInfo {
  type: string;
  acl: ACLConfig;
  feature: FeatureConfig;
  fee: FeeConfig;
  funding_fee: FundingFeeConfig;
  id: { id: string };
  white_list: WhiteListConfig;
}

/**
 * 订单查询参数
 */
export interface QueryOrderParams {
  /** 订单管理器 ID */
  orderManagerId: string;
  /** 订单 ID */
  orderId: string;
  /** 是否为增仓订单 */
  isIncreaseOrder: boolean;
}

/**
 * 增仓订单信息
 */
export interface IncreaseOrderInfo {
  /** 用户地址 */
  user: string;
  /** 关联的仓位 ID (可选) */
  position?: string;
  /** 输入金额 */
  amount: string;
  /** 抵押品代币类型 */
  collateralCoin: string;
  /** 指数代币类型 */
  indexCoin: string;
  /** 仓位大小变化 */
  sizeDelta: string;
  /** 是否为做多 */
  isLong: boolean;
  /** 触发价格 */
  triggerPrice: string;
  /** 是否在价格上穿时触发 */
  triggerAboveThreshold: boolean;
}

/**
 * 减仓订单信息
 */
export interface DecreaseOrderInfo {
  /** 用户地址 */
  user: string;
  /** 关联的仓位 ID */
  position: string;
  /** 仓位大小变化 */
  sizeDelta: string;
  /** 抵押品变化 */
  collateralDelta: string;
  /** 触发价格 */
  triggerPrice: string;
  /** 是否在价格上穿时触发 */
  triggerAboveThreshold: boolean;
}

/**
 * 订单信息枚举 - 对应合约中的 OrderInfoEnum
 */
export type OrderInfoEnum =
  | { type: 'Increase'; data: IncreaseOrderInfo }
  | { type: 'Decrease'; data: DecreaseOrderInfo };

// 基于实际测试结果的Vault对象信息类型定义
// 使用Sui原生类型，包含重要的Vault字段信息
export type VaultObjectInfo = any;

export class QueryModule {
  protected _sdk: HertzFlowSDK;

  constructor(sdk: HertzFlowSDK) {
    this._sdk = sdk;
  }

  get sdk() {
    return this._sdk;
  }

  /**
   * @dev 查询添加流动性时考虑滑点后的用户允许获得的最小数量的hzlp以及手续费(这里的手续费包括一个添加流动性的固定fee以及动态的taxFee，在合约中计算),可用于前端展示
   */
  public async queryAddLiquidityAmountAndFee(
    params: QueryAddLiquidityParams,
  ): Promise<LiquidityQueryResult> {
    const { coinType, amount, decimals, slippage } = params;
    const _amountWithDecimals = toDecimalsAmount(amount, decimals);

    const finalTx = await this.sdk.OracleModule.updateWhiteListPrices();

    finalTx.moveCall({
      package: this.sdk.sdkOptions.packageId,
      module: CONTRACT_MODULE.VAULT,
      function: CONTRACT_FUNCTION['vault'].GET_ADD_LIQUIDITY_AMOUNT_AND_FEE,
      arguments: [
        finalTx.object(this.sdk.sdkOptions.vault.package_id),
        finalTx.object(this.sdk.sdkOptions.protocolStore.package_id),
        finalTx.object(this.sdk.sdkOptions.oracleStore.package_id),
        finalTx.pure.u64(BigInt(_amountWithDecimals)),
        finalTx.object(COMMON_CONSTS.CLOCK_ID),
      ],
      typeArguments: [coinType],
    });

    const _devInspectResult =
      await this.sdk.RpcModule.devInspectTransactionBlock({
        transactionBlock: finalTx,
        sender:
          this.sdk.senderAddress ||
          this.sdk.sdkOptions.simulationAccount.address, // Simulated trading any address can be used
      });

    const returnValues =
      _devInspectResult.results?.[_devInspectResult.results.length - 1]
        ?.returnValues;
    if (!returnValues || returnValues.length < 2)
      throw new Error(
        'Invalid return values from get_add_liquidity_amount_and_fee',
      );

    const amountOutAfterFeeRaw = bcs
      .u64()
      .parse(new Uint8Array(returnValues[0][0]));
    const amountOutAfterFee = new BigNumber(amountOutAfterFeeRaw).toString(10);
    const feeRaw = bcs.u64().parse(new Uint8Array(returnValues[1][0]));
    const fee = new BigNumber(feeRaw).toString(10);
    const amountOutAfterFeeWithSlippage = new BigNumber(amountOutAfterFee)
      .multipliedBy(new BigNumber(1).minus(slippage))
      .toString(10);
    return {
      amountOutAfterFee,
      amountOutAfterFeeWithSlippage,
      fee,
    };
  }

  /**
   * @dev 查询添加流动性时考虑滑点后的用户允许获得的最小数量的asset以及手续费(这里的手续费包括一个添加流动性的固定fee以及动态的taxFee，在合约中计算)，可用于前端展示
   */
  public async queryRemoveLiquidityAmountAndFee(
    params: QueryRemoveLiquidityParams,
  ): Promise<LiquidityQueryResult> {
    const { coinType, hzlpAmount, slippage } = params;
    const { decimals } = await this.parseHzlpObject();
    const _hzlpAmountWithDecimals = toDecimalsAmount(hzlpAmount, decimals);
    const finalTx = await this.sdk.OracleModule.updateWhiteListPrices();
    finalTx.moveCall({
      package: this.sdk.sdkOptions.packageId,
      module: CONTRACT_MODULE.VAULT,
      function: CONTRACT_FUNCTION['vault'].GET_REMOVE_LIQUIDITY_AMOUNT_AND_FEE,
      arguments: [
        finalTx.object(this.sdk.sdkOptions.vault.package_id),
        finalTx.object(this.sdk.sdkOptions.protocolStore.package_id),
        finalTx.object(this.sdk.sdkOptions.oracleStore.package_id),
        finalTx.pure.u64(_hzlpAmountWithDecimals),
        finalTx.object(COMMON_CONSTS.CLOCK_ID),
      ],
      typeArguments: [coinType],
    });

    const _devInspectResult =
      await this.sdk.RpcModule.devInspectTransactionBlock({
        transactionBlock: finalTx,
        sender:
          this.sdk.senderAddress ||
          this.sdk.sdkOptions.simulationAccount.address, // Simulated trading any address can be used
      });

    const returnValues =
      _devInspectResult.results?.[_devInspectResult.results.length - 1]
        ?.returnValues;
    if (!returnValues || returnValues.length < 2)
      throw new Error(
        'Invalid return values from get_add_liquidity_amount_and_fee',
      );

    const amountOutAfterFeeRaw = bcs
      .u64()
      .parse(new Uint8Array(returnValues[0][0]));
    const amountOutAfterFee = new BigNumber(amountOutAfterFeeRaw).toString(10);
    const feeRaw = bcs.u64().parse(new Uint8Array(returnValues[1][0]));
    const fee = new BigNumber(feeRaw).toString(10);
    const amountOutAfterFeeWithSlippage = new BigNumber(amountOutAfterFee)
      .multipliedBy(new BigNumber(1).minus(slippage))
      .toString(10);
    return {
      amountOutAfterFee,
      amountOutAfterFeeWithSlippage,
      fee,
    };
  }

  /**
   * 查询 Swap 输出情况
   * @dev 根据输入代币数量计算能够获得的输出代币数量（扣除手续费后）
   * @param params Swap 查询参数
   * @returns Swap 输出结果，包含输出代币数量、考虑滑点后的输出数量、手续费和费率
   */
  public async querySwapAmountOut(
    params: SwapWithAmountInQueryParams,
  ): Promise<SwapAmountOutResult> {
    const { typeArguments, expectedAmountIn, inCoinDecimals, slippage } =
      params;
    const [coinInType, coinOutType] = typeArguments;
    const _amountInWithDecimals = toDecimalsAmount(
      expectedAmountIn,
      inCoinDecimals,
    );

    // 如果输入和输出代币相同，则直接返回输入数量
    if (coinInType === coinOutType) {
      const amountOutAfterSwap = expectedAmountIn;
      const amountOutAfterSwapWithSlippage = new BigNumber(amountOutAfterSwap)
        .div(new BigNumber(1).minus(slippage))
        .toString(10);
      return {
        amountOutAfterSwap,
        amountOutAfterSwapWithSlippage,
        fee: ZERO.toString(10),
        feeRateBps: ZERO.toString(10),
      };
    }

    // 只更新swap相关的代币价格，提高效率
    const finalTx = await this.sdk.OracleModule.updateWhiteListPrices([
      coinInType,
      coinOutType,
    ]);
    finalTx.moveCall({
      package: this.sdk.sdkOptions.packageId,
      module: CONTRACT_MODULE.VAULT,
      function: CONTRACT_FUNCTION['vault'].GET_SWAP_AMOUNT_OUT,
      arguments: [
        finalTx.object(this.sdk.sdkOptions.vault.package_id),
        finalTx.object(this.sdk.sdkOptions.protocolStore.package_id),
        finalTx.object(this.sdk.sdkOptions.oracleStore.package_id),
        finalTx.pure.u64(BigInt(_amountInWithDecimals)),
        finalTx.object(COMMON_CONSTS.CLOCK_ID),
      ],
      typeArguments: [coinInType, coinOutType],
    });

    const _devInspectResult =
      await this.sdk.RpcModule.devInspectTransactionBlock({
        transactionBlock: finalTx,
        sender:
          this.sdk.senderAddress ||
          this.sdk.sdkOptions.simulationAccount.address,
      });

    const returnValues =
      _devInspectResult.results?.[_devInspectResult.results.length - 1]
        ?.returnValues;

    if (!returnValues || returnValues.length < 3) {
      throw new Error(
        `Invalid return values from get_swap_amount_out. Expected 3, got ${returnValues?.length || 0}. Error: ${_devInspectResult.error || 'none'}`,
      );
    }

    const amountOutAfterSwapRaw = bcs
      .u64()
      .parse(new Uint8Array(returnValues[0][0]));
    const amountOutAfterSwap = new BigNumber(amountOutAfterSwapRaw).toString(
      10,
    );
    const feeRaw = bcs.u64().parse(new Uint8Array(returnValues[1][0]));
    const feeRateBpsRaw = bcs.u64().parse(new Uint8Array(returnValues[2][0]));
    const amountOutAfterSwapWithSlippage = new BigNumber(amountOutAfterSwap)
      .multipliedBy(new BigNumber(1).minus(slippage))
      .toString(10);

    return {
      amountOutAfterSwap,
      amountOutAfterSwapWithSlippage,
      fee: new BigNumber(feeRaw).toString(10),
      feeRateBps: new BigNumber(feeRateBpsRaw).toString(10),
    };
  }

  /**
   * 查询 Swap 输入情况
   * @dev 根据期望的输出代币数量计算需要的输入代币数量
   * @param params Swap 查询参数
   * @returns Swap结果，包含输入代币数量和费用
   */
  public async querySwapAmountIn(
    params: SwapWithAmountOutQueryParams,
  ): Promise<SwapAmountInResult> {
    const { typeArguments, expectedAmountOut, outCoinDecimals, slippage } =
      params;
    const [coinInType, coinOutType] = typeArguments;
    const _amountOutWithDecimals = toDecimalsAmount(
      expectedAmountOut,
      outCoinDecimals,
    );

    // 如果输入和输出代币相同，则直接返回输出数量
    if (coinInType === coinOutType) {
      const amountInAfterSwap = expectedAmountOut;
      const amountInAfterSwapWithSlippage = new BigNumber(amountInAfterSwap)
        .div(new BigNumber(1).minus(slippage))
        .toString(10);
      return {
        amountInAfterSwap: expectedAmountOut,
        amountInAfterSwapWithSlippage,
        fee: ZERO.toString(10),
        feeRateBps: ZERO.toString(10),
      };
    }
    // 只更新swap相关的代币价格，提高效率
    const finalTx = await this.sdk.OracleModule.updateWhiteListPrices([
      coinInType,
      coinOutType,
    ]);

    finalTx.moveCall({
      package: this.sdk.sdkOptions.packageId,
      module: CONTRACT_MODULE.VAULT,
      function: CONTRACT_FUNCTION['vault'].GET_SWAP_AMOUNT_IN_V2,
      arguments: [
        finalTx.object(this.sdk.sdkOptions.vault.package_id),
        finalTx.object(this.sdk.sdkOptions.protocolStore.package_id),
        finalTx.object(this.sdk.sdkOptions.oracleStore.package_id),
        finalTx.pure.u64(BigInt(_amountOutWithDecimals)),
        finalTx.object(COMMON_CONSTS.CLOCK_ID),
      ],
      typeArguments: [coinInType, coinOutType],
    });

    const _devInspectResult =
      await this.sdk.RpcModule.devInspectTransactionBlock({
        transactionBlock: finalTx,
        sender:
          this.sdk.senderAddress ||
          this.sdk.sdkOptions.simulationAccount.address,
      });

    const returnValues =
      _devInspectResult.results?.[_devInspectResult.results.length - 1]
        ?.returnValues;

    if (!returnValues || returnValues.length < 3) {
      throw new Error(
        `Invalid return values from get_swap_amount_in_v2. Expected 3, got ${returnValues?.length || 0}. Error: ${_devInspectResult.error || 'none'}`,
      );
    }

    const amountInAfterFeeRaw = bcs
      .u64()
      .parse(new Uint8Array(returnValues[0][0]));
    const amountInAfterSwap = new BigNumber(amountInAfterFeeRaw).toString(10);
    const feeRaw = bcs.u64().parse(new Uint8Array(returnValues[1][0]));
    const feeRateBpsRaw = bcs.u64().parse(new Uint8Array(returnValues[2][0]));
    const amountInAfterSwapWithSlippage = new BigNumber(amountInAfterSwap)
      .div(new BigNumber(1).minus(slippage))
      .toString(10);

    return {
      amountInAfterSwap,
      amountInAfterSwapWithSlippage,
      fee: new BigNumber(feeRaw).toString(10),
      feeRateBps: new BigNumber(feeRateBpsRaw).toString(10),
    };
  }

  /**
   * 查询最大 Swap 输入金额
   * @dev 查询在当前流动性条件下，最大可以输入多少代币进行 swap
   * @param params 最大 Swap 输入查询参数
   * @returns 最大输入金额（带精度）
   */
  public async queryMaxSwapIn(params: MaxSwapInParams): Promise<string> {
    const { coinInType, coinOutType } = params;

    // 只更新swap相关的代币价格，提高效率
    const finalTx = await this.sdk.OracleModule.updateWhiteListPrices([
      coinInType,
      coinOutType,
    ]);

    finalTx.moveCall({
      package: this.sdk.sdkOptions.packageId,
      module: CONTRACT_MODULE.VAULT,
      function: CONTRACT_FUNCTION['vault'].GET_MAX_SWAP_IN,
      arguments: [
        finalTx.object(this.sdk.sdkOptions.vault.package_id),
        finalTx.object(this.sdk.sdkOptions.protocolStore.package_id),
        finalTx.object(this.sdk.sdkOptions.oracleStore.package_id),
        finalTx.object(COMMON_CONSTS.CLOCK_ID),
      ],
      typeArguments: [coinInType, coinOutType],
    });

    const _devInspectResult =
      await this.sdk.RpcModule.devInspectTransactionBlock({
        transactionBlock: finalTx,
        sender:
          this.sdk.senderAddress ||
          this.sdk.sdkOptions.simulationAccount.address,
      });

    const returnValues =
      _devInspectResult.results?.[_devInspectResult.results.length - 1]
        ?.returnValues;

    if (!returnValues || returnValues.length < 1) {
      throw new Error(
        `Invalid return values from get_max_swap_in. Expected 1, got ${returnValues?.length || 0}. Error: ${_devInspectResult.error || 'none'}`,
      );
    }

    const maxAmountInRaw = bcs.u64().parse(new Uint8Array(returnValues[0][0]));
    return new BigNumber(maxAmountInRaw).toString(10);
  }

  /**
   * 查询AUM和LP代币价格
   * @dev 查询资产管理总额(AUM)和HZLP代币的当前价格，价格精度为20位小数
   * @returns AUM和LP价格数据（包含带精度和格式化后的值）
   */
  public async queryAumAndLpPrice(): Promise<{
    aum: {
      aumWithDecimal: string;
      aumFormatted: string;
    };
    lpPrice: {
      lpPriceWithDecimal: string;
      lpPriceFormatted: string;
    };
  }> {
    const finalTx = await this.sdk.OracleModule.updateWhiteListPrices();

    finalTx.moveCall({
      package: this.sdk.sdkOptions.packageId,
      module: CONTRACT_MODULE.VAULT,
      function: CONTRACT_FUNCTION['vault'].GET_LP_PRICE,
      arguments: [
        finalTx.object(this.sdk.sdkOptions.vault.package_id),
        finalTx.object(this.sdk.sdkOptions.oracleStore.package_id),
        finalTx.object(COMMON_CONSTS.CLOCK_ID),
      ],
    });

    const _devInspectResult =
      await this.sdk.RpcModule.devInspectTransactionBlock({
        transactionBlock: finalTx,
        sender:
          this.sdk.senderAddress ||
          this.sdk.sdkOptions.simulationAccount.address,
      });

    const returnValues =
      _devInspectResult.results?.[_devInspectResult.results.length - 1]
        ?.returnValues;

    if (!returnValues || returnValues.length < 1) {
      throw new Error(
        `Invalid return values from get_lp_price. Expected 1, got ${returnValues?.length || 0}. Error: ${_devInspectResult.error || 'none'}`,
      );
    }

    const lpPriceRaw = bcs.u128().parse(new Uint8Array(returnValues[0][0]));

    // 通过parseVaultObject获取HZLP总供应量来计算真实AUM
    const vaultInfo = await this.parseVaultObject();
    const hzlpTotalSupply =
      vaultInfo?.content?.fields?.hzlp?.fields?.treasury?.fields?.total_supply
        ?.fields?.value || '0';

    // 计算真实AUM: AUM = LP价格 × HZLP总供应量 ÷ 10^20
    const aumWithDecimal = new BigNumber(lpPriceRaw)
      .multipliedBy(hzlpTotalSupply)
      .dividedBy(new BigNumber(10).pow(PRICE_PRECISION_POWER));

    return {
      aum: {
        aumWithDecimal: aumWithDecimal.toString(10),
        aumFormatted: fromDecimalsAmount(aumWithDecimal.toString(10), 0), // AUM已经是USD格式
      },
      lpPrice: {
        lpPriceWithDecimal: new BigNumber(lpPriceRaw).toString(10),
        lpPriceFormatted: fromDecimalsAmount(lpPriceRaw, PRICE_PRECISION_POWER),
      },
    };
  }

  public async parseVaultObject(): Promise<VaultObjectInfo> {
    const vaultId = this.sdk.sdkOptions.vault.package_id;

    console.log(`🔍 开始解析Vault对象: ${vaultId}`);
    try {
      const objectResponse = await this.sdk.RpcModule.getObject({
        id: vaultId,
        options: {
          showContent: true,
          showDisplay: true,
          showOwner: true,
          showPreviousTransaction: true,
          showStorageRebate: true,
          showType: true,
        },
      });
      return objectResponse.data;
    } catch (error) {
      console.error('❌ Failed to parse Vault object:', error);
      throw new HertzflowError(
        `Failed to parse Vault object: ${error.message}`,
      );
    }
  }

  /**
   * 解析HZLP对象信息并打印详细信息
   * @param hzlpId HZLP对象ID，默认使用配置中的HZLP_ID
   * @returns 解析后的HZLP对象信息
   */
  public async parseHzlpObject(hzlpId?: string): Promise<HzlpObjectInfo> {
    const objectId = hzlpId || this.sdk.sdkOptions.hzlp.package_id;

    console.log(`🔍 开始解析HZLP对象: ${objectId}`);

    try {
      // 获取对象详细信息
      const objectResponse = await this.sdk.RpcModule.getObject({
        id: objectId,
        options: {
          showContent: true,
          showDisplay: true,
          showOwner: true,
          showPreviousTransaction: true,
          showStorageRebate: true,
          showType: true,
        },
      });

      if (!objectResponse.data) {
        throw new HertzflowError(`Unable to find the object: ${objectId}`);
      }

      const objectData = objectResponse.data;

      const hzlpInfo = {
        objectId: objectData.objectId,
        version: objectData.version,
        digest: objectData.digest,
        type: objectData.type || '',
        owner: objectData.owner,
        content: objectData.content as SuiParsedData,
        display: objectData.display,
      };

      if (
        hzlpInfo.content &&
        typeof hzlpInfo.content === 'object' &&
        'dataType' in hzlpInfo.content &&
        hzlpInfo.content.dataType === 'moveObject' &&
        'fields' in hzlpInfo.content
      ) {
        return {
          type: hzlpInfo.type,
          decimals: (hzlpInfo.content as any).fields.decimals,
          description: (hzlpInfo.content as any).fields.description,
          id: (hzlpInfo.content as any).fields.id,
          name: (hzlpInfo.content as any).fields.name,
          symbol: (hzlpInfo.content as any).fields.symbol,
        };
      } else {
        throw new HertzflowError(
          `HZLP object content is not a MoveStruct or does not have fields property. Content type: ${hzlpInfo.content?.dataType || 'unknown'}`,
        );
      }
    } catch (error) {
      console.error('❌ Failed to parse HZLP object:', error);
      throw error;
    }
  }

  /**
   * 解析ProtocolStore对象信息
   * @param protocolStoreId ProtocolStore对象ID，默认使用配置中的protocolStore.package_id
   * @returns 解析后的ProtocolStore对象信息
   */
  public async parseProtocolStoreObject(
    protocolStoreId?: string,
  ): Promise<ProtocolStoreObjectInfo> {
    const objectId =
      protocolStoreId || this.sdk.sdkOptions.protocolStore.package_id;

    console.log(`🔍 开始解析ProtocolStore对象: ${objectId}`);

    try {
      // 获取对象详细信息
      const objectResponse = await this.sdk.RpcModule.getObject({
        id: objectId,
        options: {
          showContent: true,
          showDisplay: true,
          showOwner: true,
          showPreviousTransaction: true,
          showStorageRebate: true,
          showType: true,
        },
      });

      if (!objectResponse.data) {
        throw new HertzflowError(
          `Unable to find the ProtocolStore object: ${objectId}`,
        );
      }

      const objectData = objectResponse.data;

      // 检查 content 是否为 MoveStruct 类型并且有 fields 属性
      if (
        objectData.content &&
        typeof objectData.content === 'object' &&
        'dataType' in objectData.content &&
        objectData.content.dataType === 'moveObject' &&
        'fields' in objectData.content
      ) {
        const fields = (objectData.content as any).fields;

        const protocolStoreInfo: ProtocolStoreObjectInfo = {
          type: objectData.type || '',
          acl: fields.acl,
          feature: fields.feature,
          fee: fields.fee,
          funding_fee: fields.funding_fee,
          id: fields.id,
          white_list: fields.white_list,
        };

        console.log('✅ ProtocolStore对象解析成功:', {
          objectId: objectData.objectId,
          type: protocolStoreInfo.type,
          version: objectData.version,
          contentType: objectData.content?.dataType || 'unknown',
        });

        console.log('📋 ProtocolStore Fields 详细信息:');
        console.log(JSON.stringify(fields, null, 2));

        return protocolStoreInfo;
      } else {
        throw new HertzflowError(
          `ProtocolStore object content is not a MoveStruct or does not have fields property. Content type: ${objectData.content?.dataType || 'unknown'}`,
        );
      }
    } catch (error) {
      console.error('❌ Failed to parse ProtocolStore object:', error);
      throw error;
    }
  }

  // ===== 订单查询方法 =====

  /**
   * 查询订单信息
   * @param params 订单查询参数
   * @returns 订单信息枚举，包含增仓或减仓订单的详细信息
   */
  public async queryOrder(params: QueryOrderParams): Promise<OrderInfoEnum> {
    const { orderManagerId, orderId, isIncreaseOrder } = params;

    console.log(
      `🔍 查询订单信息: 订单ID=${orderId}, 是否增仓=${isIncreaseOrder}...`,
    );

    try {
      const tx = new Transaction();
      tx.moveCall({
        package: this.sdk.sdkOptions.packageId,
        module: CONTRACT_MODULE.ORDER,
        function: CONTRACT_FUNCTION['order'].GET_ORDER,
        arguments: [
          tx.object(orderManagerId),
          tx.pure.id(orderId),
          tx.pure.bool(isIncreaseOrder),
        ],
      });

      const result = await this.sdk.RpcModule.devInspectTransactionBlock({
        transactionBlock: tx,
        sender:
          this.sdk.senderAddress ||
          this.sdk.sdkOptions.simulationAccount.address,
      });

      if (result.effects?.status?.status === 'failure') {
        throw new HertzflowError(
          `Failed to query order: ${result.effects.status.error}`,
        );
      }

      const returnValues = result.results?.[0]?.returnValues;
      if (!returnValues || returnValues.length < 1) {
        throw new HertzflowError('Invalid return values from get_order');
      }

      // 解析返回的 OrderInfoEnum
      const orderInfoBytes = new Uint8Array(returnValues[0][0]);
      const orderInfo = this.parseOrderInfoEnum(
        orderInfoBytes,
        isIncreaseOrder,
      );

      console.log(`✅ 订单查询成功:`, orderInfo);
      return orderInfo;
    } catch (error) {
      console.error('❌ 查询订单信息失败:', error);
      throw error;
    }
  }

  /**
   * 解析 OrderInfoEnum 字节数据
   * @param bytes 字节数据
   * @param isIncreaseOrder 是否为增仓订单
   * @returns 解析后的订单信息
   */
  private parseOrderInfoEnum(
    bytes: Uint8Array,
    isIncreaseOrder: boolean,
  ): OrderInfoEnum {
    // 这里需要根据实际的 BCS 序列化格式来解析
    // 由于 OrderInfoEnum 是一个枚举，需要先解析枚举标识符，然后解析对应的数据结构

    // 简化实现：假设我们可以通过其他方式获取订单信息
    // 在实际实现中，需要使用正确的 BCS 解析逻辑

    if (isIncreaseOrder) {
      // 解析 IncreaseOrderInfo
      return {
        type: 'Increase',
        data: {
          user: '0x0000000000000000000000000000000000000000000000000000000000000000', // 需要从 bytes 解析
          position: undefined, // 可选字段
          amount: '0', // 需要从 bytes 解析
          collateralCoin: '0x2::sui::SUI', // 需要从 bytes 解析
          indexCoin: '0x2::sui::SUI', // 需要从 bytes 解析
          sizeDelta: '0', // 需要从 bytes 解析
          isLong: true, // 需要从 bytes 解析
          triggerPrice: '0', // 需要从 bytes 解析
          triggerAboveThreshold: false, // 需要从 bytes 解析
        },
      };
    } else {
      // 解析 DecreaseOrderInfo
      return {
        type: 'Decrease',
        data: {
          user: '0x0000000000000000000000000000000000000000000000000000000000000000', // 需要从 bytes 解析
          position:
            '0x0000000000000000000000000000000000000000000000000000000000000000', // 需要从 bytes 解析
          sizeDelta: '0', // 需要从 bytes 解析
          collateralDelta: '0', // 需要从 bytes 解析
          triggerPrice: '0', // 需要从 bytes 解析
          triggerAboveThreshold: false, // 需要从 bytes 解析
        },
      };
    }
  }
  // ===== 费用查询方法 =====

  /**
   * 获取指定费用类型的费率 (基点)
   * @param feeKey 费用类型枚举值
   * @param protocolStoreId 可选的 ProtocolStore ID，默认使用配置中的 ID
   * @returns 费率 (基点，例如 10 表示 0.1%)
   */
  public async getFeeRate(
    feeKey: FeeKey,
    protocolStoreId?: string,
  ): Promise<string> {
    console.log(`🔍 查询费用类型 ${FeeKey[feeKey]} (${feeKey}) 的费率...`);

    try {
      // 首先获取 ProtocolStore 对象信息来提取 FeeConfig
      const protocolStoreInfo =
        await this.parseProtocolStoreObject(protocolStoreId);

      // 从 ProtocolStore 的 fee 字段中直接读取费率
      const feeConfig = protocolStoreInfo.fee.fields;
      let feeRate: string;

      switch (feeKey) {
        case FeeKey.LiquidationFee:
          feeRate = feeConfig.liquidation_fee_bps;
          break;
        case FeeKey.TaxFee:
          feeRate = feeConfig.tax_bps;
          break;
        case FeeKey.StableTaxFee:
          feeRate = feeConfig.stable_tax_bps;
          break;
        case FeeKey.SwapFee:
          feeRate = feeConfig.swap_fee_bps;
          break;
        case FeeKey.StableSwapFee:
          feeRate = feeConfig.stable_swap_fee_bps;
          break;
        case FeeKey.AddRemoveFee:
          feeRate = feeConfig.add_remove_fee_bps;
          break;
        case FeeKey.IncreasePositionFee:
          feeRate = feeConfig.increase_position_bps;
          break;
        case FeeKey.DecreasePositionFee:
          feeRate = feeConfig.decrease_position_bps;
          break;
        case FeeKey.ProtocolFee:
          feeRate = feeConfig.protocol_fee_bps;
          break;
        default:
          throw new HertzflowError(`Invalid fee key: ${feeKey}`);
      }

      console.log(`✅ 费用类型 ${FeeKey[feeKey]} 的费率: ${feeRate} 基点`);
      return feeRate;
    } catch (error) {
      console.error(`❌ 获取费用类型 ${FeeKey[feeKey]} 的费率失败:`, error);
      throw error;
    }
  }

  /**
   * 获取所有费用类型的费率
   * @param protocolStoreId 可选的 ProtocolStore ID，默认使用配置中的 ID
   * @returns 包含所有费用类型及其费率的对象
   */
  public async getAllFeeRates(
    protocolStoreId?: string,
  ): Promise<Record<string, string>> {
    console.log('🔍 获取所有费用类型的费率...');

    const feeRates: Record<string, string> = {};

    // 获取所有有效的费用类型 (0-8)
    const validFeeKeys = [
      FeeKey.LiquidationFee,
      FeeKey.TaxFee,
      FeeKey.StableTaxFee,
      FeeKey.SwapFee,
      FeeKey.StableSwapFee,
      FeeKey.AddRemoveFee,
      FeeKey.IncreasePositionFee,
      FeeKey.DecreasePositionFee,
      FeeKey.ProtocolFee,
    ];

    try {
      for (const feeKey of validFeeKeys) {
        const rate = await this.getFeeRate(feeKey, protocolStoreId);
        feeRates[FeeKey[feeKey]] = rate;
      }

      console.log('✅ 所有费用类型的费率:', feeRates);
      return feeRates;
    } catch (error) {
      console.error('❌ 获取所有费用类型的费率失败:', error);
      throw error;
    }
  }
}
