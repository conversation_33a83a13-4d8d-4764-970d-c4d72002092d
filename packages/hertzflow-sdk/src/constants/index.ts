/**
 * @dev HertzFlow API endpoints
 */
export const HERTZFLOW_API_ENDPOINTS = {
  MAINNET:
    process.env.HERTZFLOW_API_MAINNET || 'https://api-mainnet.hertzflow.xyz',
  TESTNET:
    process.env.HERTZFLOW_API_TESTNET || 'https://api-testnet.hertzflow.xyz',
};

/**
 * @dev Contract IDs
 */
export const ENV_CONFIG = {
  MAINNET: {
    PACKAGE_ID: 'TODO',
    HZLP_ID: 'TODO',
    HZLP_MANAGER_ID: 'TODO',
    ORACLE_STORE_ID: 'TODO',
    VERSION_ID: 'TODO',
    PROTOCOL_STORE_ID: 'TODO',
    ORACLE_PACKAGE_ID: 'TODO',
    ORACLE_ID: 'TODO',
    PACKAGE_MARK_ID: 'TODO',
    ADMIN_CAP_ID: 'TODO',
    VAULT_ID: 'TODO',
    <PERSON><PERSON><PERSON>_TYPE: 'TODO',
  },
  TESTNET: {
    PACKAGE_ID:
      '0xfe850ae6b1e1bdd3860fd1e3467dc3842c8636fed70473f657a7806b58212cf5',
    HZLP_ID:
      '0x0f64cbffcdecace7c53e62603409678c836f331540c48cdd2dbaceec04519619',
    HZLP_MANAGER_ID:
      '0xcdc7920c4b0ce266ff010171cb337dcd50dfa36509660c5f504fc37a09460e0d',
    ORACLE_STORE_ID:
      '0xb029c5f1b02e1527a975d03ad60982b55c668155a45b3f8d6131d294bfa339bc',
    VERSION_ID:
      '0x2bbb55c4cb2db8b81e517185312576fd0ede1c968ffc1e3ec1faff8e56846a58',
    PROTOCOL_STORE_ID:
      '0xcfe6eef908a1f24250636bdee85fcda9a88bf00df648090c54c1650ffa130dcc',
    ADMIN_CAP_ID:
      '0x839ac86c0f6f7a1421ab89d6fd498b4bfec404ad692818f365aa107d9c9dab1b',
    ORACLE_PACKAGE_ID:
      '0xebbc14449b84768741294d00767428de9479c2f33ceafa2e6f0762e0debe6067',
    ORACLE_ID:
      '0x06f56c28d392ceae89c7783a0af4d83145665baae8bb1c4435e41c3d528175b7',
    PACKAGE_MARK_ID:
      '0x77170eaa7265866e8bcc1555d397b1130013b610f4bd831ffa5a3f9e47afadc7',
    VAULT_ID:
      '0x742fbcd46ced2522e2342184be7cd000b46e286105dbac0e3b2b9b62da87daa0',
    HZLP_TYPE: `0x62c25d0b9513778eb70ce7a332d3014e004c9bf54807e2d2939037c966f9d090::hzlp::HZLP`,
  },
};

/**
 * @dev Contract modules
 */
export enum CONTRACT_MODULE {
  VAULT = 'vault',
  ORACLE = 'oracle',
  FEE = 'fee',
  ORDER = 'order',
}

/**
 * @dev Contract functions which are available for frontend
 */
export const CONTRACT_FUNCTION = {
  [CONTRACT_MODULE.VAULT]: {
    // CREATE_VAULT: 'create_vault',
    // ADD_CUSTODY: 'add_custody',
    // UPDATE_CUSTODY: 'update_custody',
    ADD_LIQUIDITY: 'add_liquidity',
    REMOVE_LIQUIDITY: 'remove_liquidity',
    SWAP: 'swap',
    CREATE_POSITION_REQUEST: 'create_position_request',
    INCREASE_POSITION_REQUEST: 'increase_position_request',
    DECREASE_POSITION_REQUEST: 'decrease_position_request',
    CANCEL_INCREASE_POSITION: 'cancel_increase_position',
    CANCEL_DECREASE_POSITION: 'cancel_decrease_position',
    GET_ADD_LIQUIDITY_AMOUNT_AND_FEE: 'get_add_liquidity_amount_and_fee',
    GET_REMOVE_LIQUIDITY_AMOUNT_AND_FEE: 'get_remove_liquidity_amount_and_fee',
    GET_LP_PRICE: 'get_lp_price',
    GET_SWAP_AMOUNT_OUT: 'get_swap_amount_out',
    GET_SWAP_AMOUNT_IN: 'get_swap_amount_in',
    GET_SWAP_AMOUNT_IN_V2: 'get_swap_amount_in_v2',
    GET_MAX_SWAP_IN: 'get_max_swap_in',
    GET_ADD_FEE_BPS: 'get_add_fee_bps',
    GET_REMOVE_FEE_BPS: 'get_remove_fee_bps',
    // EXECUTE_INCREASE_POSITION: 'execute_increase_position',
    // EXECUTE_DECREASE_POSITION: 'execute_decrease_position',
    // LIQUIDATE_POSITION: 'liquidate_position',
    // UPDATE_MAX_AUM_USD: 'update_max_aum_usd',
  },
  [CONTRACT_MODULE.ORACLE]: {
    UPDATE_PRICE: 'update_price',
  },
  [CONTRACT_MODULE.FEE]: {
    GET_FEE_AMOUNT: 'get_fee_amount',
    GET_FROM_AFTER_FEE_AMOUNT: 'get_from_after_fee_amount',
    GET_POSITION_FEE: 'get_position_fee',
  },
  [CONTRACT_MODULE.ORDER]: {
    GET_ORDER: 'get_order',
  },
};

export const COMMON_CONSTS = {
  FEE_BPS_POWER: 10000,
  FEE_RATE_DECIMAL: 10000,
  CLOCK_ID:
    '0x0000000000000000000000000000000000000000000000000000000000000006',
  SUI_TYPE_ARG: '0x2::sui::SUI',
  SUI_TYPE_ARG_LONG:
    '0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI',
  MOCK_COIN_TYPE:
    '0xe02e8f4b0658897fa9bfba5ccb2c21b5e9f1c8fa463e624b13e28a9ca8198885::my_token::MY_TOKEN',
  SUI_PRIVATE_KEY_PREFIX: 'suiprivkey1',
};

export enum HERTZFLOW_SUFFIX {
  USD = '/USD',
}
