import type { HertzFlowSDK } from '../sdk';
import {
  type LimitCheckOptions,
  type LimitCheckResult,
  type SwapLimitCheckParams,
  type AddLiquidityLimitCheckParams,
  type PositionLimitCheckParams,
  LimitCheckError,
  LimitCheckErrorType,
  DEFAULT_LIMIT_CHECK_OPTIONS,
} from '../types/limitCheck';

/**
 * 限制检查服务
 * 提供统一的限制检查功能
 */
export class LimitCheckService {
  constructor(private sdk: HertzFlowSDK) {}

  /**
   * 检查 Swap 操作限制
   */
  async checkSwapLimits(
    params: SwapLimitCheckParams,
    options: LimitCheckOptions = {},
  ): Promise<LimitCheckResult> {
    const config = { ...DEFAULT_LIMIT_CHECK_OPTIONS, ...options };

    if (!config.enableLimitCheck) {
      return this.createSkippedResult('swap', params.amountIn);
    }

    try {
      if (config.verbose) {
        console.log('🔍 检查 Swap 限制:', params);
      }

      // 🚀 优化：如果输入和输出代币类型相同，不需要swap，直接通过
      if (params.coinInType === params.coinOutType) {
        if (config.verbose) {
          console.log('✅ 输入输出代币相同，无需swap，直接通过检查');
        }

        return {
          isValid: true,
          maxLimit: 'no-swap-needed',
          reason: `输入输出代币相同 (${params.coinInType})，无需 swap 操作`,
          originalAmount: params.amountIn,
          checkType: 'swap',
        };
      }

      // 查询最大 swap 输入限制
      const maxSwapIn = await this.sdk.QueryModule.queryMaxSwapIn({
        coinInType: params.coinInType,
        coinOutType: params.coinOutType,
      });

      const maxSwapInFormatted =
        Number(maxSwapIn) / Math.pow(10, params.inCoinDecimals);
      const amountInNumber = Number(params.amountIn);

      // 检查是否超过限制
      if (Number(maxSwapIn) === 0) {
        const result: LimitCheckResult = {
          isValid: false,
          maxLimit: '0',
          reason: '最大 swap 输入为 0，无法进行 swap 操作',
          originalAmount: params.amountIn,
          checkType: 'swap',
        };

        if (config.throwOnLimitExceeded) {
          throw new LimitCheckError(
            result.reason,
            LimitCheckErrorType.SWAP_LIMIT_EXCEEDED,
            result,
          );
        }

        return result;
      }

      // 检查原始金额是否超过最大限制
      if (amountInNumber <= maxSwapInFormatted) {
        return {
          isValid: true,
          maxLimit: maxSwapInFormatted.toString(),
          reason: `金额 ${params.amountIn} 在限制范围内`,
          originalAmount: params.amountIn,
          checkType: 'swap',
        };
      }

      // 如果超过限制
      const result: LimitCheckResult = {
        isValid: false,
        maxLimit: maxSwapInFormatted.toString(),
        reason: `金额 ${params.amountIn} 超过最大限制 ${maxSwapInFormatted}`,
        originalAmount: params.amountIn,
        checkType: 'swap',
      };

      if (config.throwOnLimitExceeded) {
        throw new LimitCheckError(
          result.reason,
          LimitCheckErrorType.SWAP_LIMIT_EXCEEDED,
          result,
        );
      }

      return result;
    } catch (error) {
      if (error instanceof LimitCheckError) {
        throw error;
      }

      const result: LimitCheckResult = {
        isValid: false,
        maxLimit: 'error',
        reason: `查询限制失败: ${(error as Error).message}`,
        originalAmount: params.amountIn,
        checkType: 'swap',
      };

      if (config.throwOnLimitExceeded) {
        throw new LimitCheckError(
          result.reason,
          LimitCheckErrorType.QUERY_FAILED,
          result,
        );
      }

      return result;
    }
  }

  /**
   * 检查添加流动性操作限制
   */
  async checkAddLiquidityLimits(
    params: AddLiquidityLimitCheckParams,
    options: LimitCheckOptions = {},
  ): Promise<LimitCheckResult> {
    const config = { ...DEFAULT_LIMIT_CHECK_OPTIONS, ...options };

    if (!config.enableLimitCheck) {
      return this.createSkippedResult('addLiquidity', params.assetAmount);
    }

    try {
      if (config.verbose) {
        console.log('🔍 检查添加流动性限制:', params);
      }

      // 使用现有的查询函数来检测限制
      await this.sdk.QueryModule.queryAddLiquidityAmountAndFee({
        coinType: params.coinType,
        amount: params.assetAmount,
        decimals: params.assetDecimals,
        slippage: 0.01,
      });

      // 如果查询成功，说明金额在限制范围内
      return {
        isValid: true,
        maxLimit: 'unknown',
        reason: `金额 ${params.assetAmount} 在限制范围内`,
        originalAmount: params.assetAmount,
        checkType: 'addLiquidity',
      };
    } catch (error) {
      const errorMessage = (error as Error).message;

      // 检查是否是预期的限制错误
      if (this.isAddLiquidityLimitError(errorMessage)) {
        const result: LimitCheckResult = {
          isValid: false,
          maxLimit: '0',
          reason: '金额超过池子最大 USD 限制，无法添加流动性',
          originalAmount: params.assetAmount,
          checkType: 'addLiquidity',
        };

        if (config.throwOnLimitExceeded) {
          throw new LimitCheckError(
            result.reason,
            LimitCheckErrorType.ADD_LIQUIDITY_LIMIT_EXCEEDED,
            result,
          );
        }

        return result;
      }

      // 其他错误
      const result: LimitCheckResult = {
        isValid: false,
        maxLimit: 'error',
        reason: `查询限制失败: ${errorMessage}`,
        originalAmount: params.assetAmount,
        checkType: 'addLiquidity',
      };

      if (config.throwOnLimitExceeded) {
        throw new LimitCheckError(
          result.reason,
          LimitCheckErrorType.QUERY_FAILED,
          result,
        );
      }

      return result;
    }
  }

  /**
   * 检查开仓操作限制
   */
  async checkPositionLimits(
    params: PositionLimitCheckParams,
    options: LimitCheckOptions = {},
  ): Promise<LimitCheckResult> {
    const config = { ...DEFAULT_LIMIT_CHECK_OPTIONS, ...options };

    if (!config.enableLimitCheck) {
      return this.createSkippedResult('position', params.amountIn);
    }

    // 检查是否需要 swap
    const needsSwap = params.payCoinType !== params.collateralCoinType;

    if (needsSwap) {
      if (config.verbose) {
        console.log('🔍 开仓需要 swap，检查 swap 限制');
      }

      // 如果需要 swap，检查 swap 限制
      return await this.checkSwapLimits(
        {
          coinInType: params.payCoinType,
          coinOutType: params.collateralCoinType,
          amountIn: params.amountIn,
          inCoinDecimals: params.inCoinDecimals,
        },
        options,
      );
    }

    // 如果不需要 swap，直接通过
    return {
      isValid: true,
      maxLimit: 'no-limit',
      reason: `开仓不需要 swap，金额 ${params.amountIn} 可以使用`,
      originalAmount: params.amountIn,
      checkType: 'position',
    };
  }

  /**
   * 创建跳过检查的结果
   */
  private createSkippedResult(
    checkType: 'swap' | 'addLiquidity' | 'position',
    amount: string,
  ): LimitCheckResult {
    return {
      isValid: true,
      maxLimit: 'not-checked',
      reason: '限制检查已禁用',
      originalAmount: amount,
      checkType,
    };
  }

  /**
   * 检查是否是添加流动性限制错误
   */
  private isAddLiquidityLimitError(errorMessage: string): boolean {
    const limitErrorKeywords = [
      'EExceedsMaxUsdAmount',
      'exceeds max usd amount',
      'pool reached max USD limit',
      'max_usd_amount',
    ];

    return limitErrorKeywords.some((keyword) => errorMessage.includes(keyword));
  }
}
