import type { Config } from 'jest';

const config: Config = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  setupFiles: ['dotenv/config'],
  testMatch: ['**/__tests__/integration/**/*.test.ts'],
  testTimeout: 60000, // 60秒超时，适合网络交互
  verbose: true,
  collectCoverage: false, // 集成测试不需要覆盖率
  maxWorkers: 1, // 串行执行，避免网络请求冲突
  // 集成测试特定配置
  globalSetup: undefined,
  globalTeardown: undefined,
  // 环境变量检查
  setupFilesAfterEnv: ['<rootDir>/__tests__/setup/integration.setup.ts'],
};

export default config;
