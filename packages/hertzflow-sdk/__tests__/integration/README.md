# HertzFlow SDK 集成测试

这个目录包含了 HertzFlow SDK 的集成测试，用于验证 SDK 在真实测试网环境中的功能。

## 🚀 快速开始

### 1. 环境准备

#### 设置私钥

集成测试需要一个有效的 Sui 私钥来执行真实交易：

```bash
# 方法1: 环境变量
export PRIVATE_KEY="your_private_key_here"

# 方法2: .env 文件
echo "PRIVATE_KEY=your_private_key_here" > .env
```

#### 获取测试网代币

确保测试账户有足够的 SUI 余额：

1. 访问 [Sui 测试网水龙头](https://docs.sui.io/guides/developer/getting-started/get-coins)
2. 输入你的测试地址获取免费的测试网 SUI

### 2. 运行测试

```bash
# 运行所有集成测试
npm run test:integration

# 监视模式运行集成测试
npm run test:integration:watch

# 只运行单元测试（排除集成测试）
npm run test:unit
```

## 📋 测试用例

### VaultModule 集成测试

#### 1. 真实添加流动性交易测试

- **功能**: 在测试网上执行完整的添加流动性流程
- **验证**:
  - 查询预估结果正确
  - 交易创建成功
  - 交易执行成功
  - 余额变化正确
  - 事件发出正确

#### 2. 余额不足错误处理测试

- **功能**: 测试当账户余额不足时的错误处理
- **验证**:
  - 查询阶段正常
  - 执行阶段正确抛出错误
  - 错误信息友好

#### 3. 滑点保护机制测试

- **功能**: 测试极低滑点设置下的保护机制
- **验证**:
  - 滑点计算正确
  - 保护机制正常工作
  - 交易在不利条件下被拒绝

## 🔧 测试配置

### Jest 配置 (`jest.integration.config.ts`)

- **超时时间**: 60秒（适合网络交互）
- **执行模式**: 串行执行（避免网络冲突）
- **覆盖率**: 禁用（集成测试不需要）
- **详细输出**: 启用

### 环境设置 (`integration.setup.ts`)

- 环境变量检查
- 网络连接验证
- 全局错误处理

## 📊 测试数据

### 测试参数

```typescript
// 正常测试
assetAmount: '0.001'; // 0.001 SUI
slippage: 0.05; // 5% 滑点

// 余额不足测试
assetAmount: '1000'; // 1000 SUI

// 滑点保护测试
assetAmount: '0.0001'; // 0.0001 SUI
slippage: 0.0001; // 0.01% 极低滑点
```

### 网络配置

- **测试网**: Sui Testnet
- **RPC 端点**: https://fullnode.testnet.sui.io:443
- **API 端点**: https://api-testnet.hertzflow.xyz

## ⚠️ 注意事项

### 成本

- 集成测试会消耗真实的 Gas 费用
- 每次测试大约消耗 0.001-0.01 SUI

### 网络依赖

- 测试依赖网络连接
- 可能因为网络延迟而较慢
- 测试网偶尔可能不稳定

### 并发限制

- 测试配置为串行执行
- 避免同时发送多个交易
- 防止 nonce 冲突

## 🐛 故障排除

### 常见错误

#### 1. `PRIVATE_KEY is not set`

**解决方案**: 设置正确的环境变量

```bash
export PRIVATE_KEY="your_private_key_here"
```

#### 2. `Insufficient balance`

**解决方案**: 从测试网水龙头获取更多 SUI

- 访问: https://docs.sui.io/guides/developer/getting-started/get-coins

#### 3. `Network timeout`

**解决方案**:

- 检查网络连接
- 重试测试
- 增加超时时间

#### 4. `Transaction failed`

**可能原因**:

- 滑点保护触发（正常）
- 价格变动过大
- 合约状态变化

### 调试技巧

#### 1. 查看详细日志

```bash
npm run test:integration -- --verbose
```

#### 2. 运行单个测试

```bash
npm run test:integration -- --testNamePattern="should execute add liquidity"
```

#### 3. 检查账户状态

测试开始前会显示账户余额信息

## 📈 持续集成

### GitHub Actions

集成测试可以在 CI/CD 中运行，但需要：

1. 设置 `PRIVATE_KEY` 密钥
2. 确保测试账户有足够余额
3. 配置适当的超时时间

### 本地开发

建议在本地开发时：

1. 使用专门的测试账户
2. 定期从水龙头补充余额
3. 监控测试成本

## 🔗 相关链接

- [Sui 测试网水龙头](https://docs.sui.io/guides/developer/getting-started/get-coins)
- [Sui 测试网浏览器](https://testnet.suivision.xyz/)
