/**
 * 测试常量定义
 * 消除魔数，统一管理测试参数
 */

// ===== 通用测试配置 =====
export const TEST_CONFIG = {
  TIMEOUT: 60000, // 60秒超时
  INTEGRATION_TIMEOUT: 120000, // 集成测试120秒超时
} as const;

// ===== 代币相关常量 =====
export const TOKEN_CONSTANTS = {
  SUI: {
    TYPE: '0x2::sui::SUI',
    DECIMALS: 9,
    SYMBOL: 'SUI',
    PRICE_USD: '4.0', // 测试用价格
  },
  BTC: {
    DECIMALS: 8,
    SYMBOL: 'BTC',
    PRICE_USD: '43000.0', // 测试用价格
  },
} as const;

// ===== 测试金额常量 =====
export const TEST_AMOUNTS = {
  // 添加流动性测试金额
  ADD_LIQUIDITY: {
    SMALL: '0.0001',
    MEDIUM: '0.001',
    LARGE: '0.01',
    VERY_LARGE: '1000', // 用于测试余额不足
  },

  // 移除流动性测试金额
  REMOVE_LIQUIDITY: {
    SMALL: '0.1',
    MEDIUM: '1',
    LARGE: '10',
  },

  // Swap 测试金额
  SWAP: {
    TINY: '0.00005',
    SMALL: '0.0001',
    MEDIUM: '0.001',
    LARGE: '0.01',
  },

  // 开仓测试金额
  POSITION: {
    SMALL: '0.001',
    MEDIUM: '0.01',
    LARGE: '0.1',
  },

  // BTC 测试金额
  BTC: {
    TINY: '0.00001',
    SMALL: '0.0001',
    MEDIUM: '0.001',
  },
} as const;

// ===== 滑点常量 =====
export const SLIPPAGE_VALUES = {
  VERY_LOW: 0.005, // 0.5%
  LOW: 0.01, // 1%
  MEDIUM: 0.02, // 2%
  HIGH: 0.05, // 5%
  VERY_HIGH: 0.1, // 10% - 用于确保测试通过
} as const;

// ===== 杠杆倍数常量 =====
export const LEVERAGE_VALUES = {
  MIN: 1,
  LOW: 2,
  MEDIUM: 5,
  HIGH: 10,
  MAX: 50,
} as const;

// ===== 测试场景配置 =====
export const TEST_SCENARIOS = {
  // 滑点测试场景
  SLIPPAGE_CASES: [
    { slippage: SLIPPAGE_VALUES.VERY_LOW, description: '0.5% 滑点' },
    { slippage: SLIPPAGE_VALUES.LOW, description: '1% 滑点' },
    { slippage: SLIPPAGE_VALUES.MEDIUM, description: '2% 滑点' },
    { slippage: SLIPPAGE_VALUES.HIGH, description: '5% 滑点' },
  ],

  // 金额测试场景（静态，用于不需要动态限制的测试）
  AMOUNT_CASES_STATIC: [
    { amount: TEST_AMOUNTS.SWAP.SMALL, description: '0.0001 SUI (小额)' },
    { amount: TEST_AMOUNTS.SWAP.MEDIUM, description: '0.001 SUI (中等)' },
    // 移除可能超限的大额测试，改为动态生成
  ],

  // 动态金额测试场景（将在运行时根据实际限制生成）
  AMOUNT_CASES: [
    { amount: TEST_AMOUNTS.SWAP.SMALL, description: '0.0001 SUI (小额)' },
    { amount: TEST_AMOUNTS.SWAP.MEDIUM, description: '0.001 SUI (中等)' },
    { amount: TEST_AMOUNTS.SWAP.LARGE, description: '0.01 SUI (大额)' }, // 将被动态替换
  ],

  // 杠杆测试场景
  LEVERAGE_CASES: [
    LEVERAGE_VALUES.MIN,
    LEVERAGE_VALUES.LOW,
    LEVERAGE_VALUES.MEDIUM,
    LEVERAGE_VALUES.HIGH,
  ],

  // BTC 金额测试场景
  BTC_AMOUNT_CASES: [
    TEST_AMOUNTS.BTC.TINY,
    TEST_AMOUNTS.BTC.SMALL,
    TEST_AMOUNTS.BTC.MEDIUM,
  ],
} as const;

// ===== 预期错误消息 =====
export const EXPECTED_ERRORS = {
  // 余额相关错误
  BALANCE: [
    'Insufficient balance',
    'InsufficientBalance',
    'insufficient funds',
    'balance',
  ],

  // 流动性相关错误
  LIQUIDITY: [
    'bag_balance_withdraw',
    '13906834406271614977', // swap 流动性不足错误代码
    'increase_pool_amount',
    '13906835406999781389', // 添加流动性超限错误代码
  ],

  // 合约业务逻辑错误
  CONTRACT_BUSINESS: [
    'Insufficient pool amount',
    'Request Already Exists',
    'Request Expired',
    'Increase Position Price Not Met',
    'Decrease Position Price Not Met',
    'Exceeded max global short size',
    'Exceeded max global long size',
    'Invalid Swap Same Token',
    'Position Not Found',
    'EExceedsMaxUsdAmount',
  ],

  // Swap 限制相关错误
  SWAP_LIMITS: [
    'exceeds max swap in',
    'max_swap_in',
    'pool has no available liquidity',
    'input pool reached max USD limit',
  ],

  // 添加流动性限制相关错误
  ADD_LIQUIDITY_LIMITS: [
    'EExceedsMaxUsdAmount',
    'exceeds max usd amount',
    'pool reached max USD limit',
    'max_usd_amount',
  ],

  // 开仓限制相关错误
  POSITION_LIMITS: [
    'exceeds max swap in',
    'position size too large',
    'insufficient collateral',
    'max leverage exceeded',
  ],
} as const;

// ===== 限制检查配置 =====
export const LIMIT_CHECK_CONFIG = {
  // Swap 限制配置
  SWAP: {
    // 当最大输入为 0 时的原因分析
    ZERO_MAX_INPUT_REASONS: [
      '输出池子 (目标代币) 可能没有可用流动性 (pool_amount <= reserved_amount)',
      '输入池子 (源代币) 可能已达到最大 USD 限制 (max_usd_amount)',
      '价格或配置问题导致无法计算有效的 swap 金额',
      '池子可能处于维护状态或未正确初始化',
    ],
    // 安全边距 - 使用最大限制的百分比作为实际测试金额
    SAFETY_MARGIN: 0.8, // 80% 的安全边距
    // 最小测试金额阈值
    MIN_TEST_AMOUNT_THRESHOLD: 0.00001, // 如果最大限制小于这个值，认为不适合测试
  },

  // 添加流动性限制配置
  ADD_LIQUIDITY: {
    // 当达到最大 USD 限制时的原因分析
    MAX_USD_LIMIT_REASONS: [
      '池子已达到最大 USD 限制 (max_usd_amount)',
      '当前池子 USD 价值 + 新增金额超过了配置的最大值',
      '需要等待其他用户移除流动性或管理员调整限制',
      '可以尝试添加更小的金额',
    ],
    // 安全边距
    SAFETY_MARGIN: 0.9, // 90% 的安全边距（比 swap 更保守）
    // 最小测试金额阈值
    MIN_TEST_AMOUNT_THRESHOLD: 0.0001,
  },

  // 开仓限制配置
  POSITION: {
    // 开仓失败的原因分析
    FAILURE_REASONS: [
      '如果需要 swap，可能受到 swap 限制影响',
      '杠杆倍数可能超过最大允许值',
      '仓位大小可能超过全局限制',
      '抵押品不足或价格波动过大',
    ],
    // 安全边距
    SAFETY_MARGIN: 0.8, // 80% 的安全边距
    // 最小测试金额阈值
    MIN_TEST_AMOUNT_THRESHOLD: 0.0001,
  },

  // 通用配置
  COMMON: {
    // 是否默认启用限制检查
    ENABLE_LIMIT_CHECK_BY_DEFAULT: true,
    // 检查失败时是否抛出错误
    THROW_ON_LIMIT_EXCEEDED: true,
  },
} as const;

// 为了向后兼容，保留原有的 SWAP_LIMIT_CONFIG
export const SWAP_LIMIT_CONFIG = LIMIT_CHECK_CONFIG.SWAP;

// ===== Gas 预算常量 =====
export const GAS_BUDGET = {
  LOW: 50000000, // 0.05 SUI
  MEDIUM: 100000000, // 0.1 SUI
  HIGH: 200000000, // 0.2 SUI
} as const;

// ===== 测试用类型参数 =====
export const TYPE_ARGUMENTS = {
  // 添加流动性
  ADD_LIQUIDITY_SUI: [TOKEN_CONSTANTS.SUI.TYPE] as [string],

  // 移除流动性
  REMOVE_LIQUIDITY_SUI: [TOKEN_CONSTANTS.SUI.TYPE] as [string],

  // Swap: SUI -> BTC
  SWAP_SUI_TO_BTC: (btcType: string) =>
    [TOKEN_CONSTANTS.SUI.TYPE, btcType] as [string, string],

  // 开仓: SUI -> BTC -> BTC
  POSITION_SUI_BTC: (btcType: string) =>
    [TOKEN_CONSTANTS.SUI.TYPE, btcType, btcType] as [string, string, string],
} as const;
