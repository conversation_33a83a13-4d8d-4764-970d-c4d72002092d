/**
 * 集成测试环境设置
 */

import { beforeAll } from '@jest/globals';

// 检查必要的环境变量
beforeAll(() => {
  console.log('🔧 集成测试环境检查...');

  // 检查私钥
  if (!process.env.PRIVATE_KEY) {
    console.error('❌ PRIVATE_KEY 环境变量未设置');
    console.log('💡 请设置 PRIVATE_KEY 环境变量:');
    console.log('   export PRIVATE_KEY="your_private_key_here"');
    console.log('   或在 .env 文件中添加: PRIVATE_KEY=your_private_key_here');
    throw new Error('PRIVATE_KEY is required for integration tests');
  }

  console.log('✅ 环境变量检查通过');
  console.log('🌐 测试网络: Sui Testnet');
  console.log('📡 RPC 端点: https://fullnode.testnet.sui.io:443');
  console.log('🔗 API 端点: https://api-testnet.hertzflow.xyz');

  console.log('\n📋 集成测试说明:');
  console.log('- 这些测试会在真实的 Sui 测试网上执行交易');
  console.log('- 需要测试账户有足够的 SUI 余额');
  console.log(
    '- 可以从测试网水龙头获取: https://docs.sui.io/guides/developer/getting-started/get-coins',
  );
  console.log('- 交易会消耗真实的 Gas 费用');
  console.log('- 测试可能因为网络延迟而较慢\n');
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的 Promise 拒绝:', reason);
  console.error('Promise:', promise);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});
