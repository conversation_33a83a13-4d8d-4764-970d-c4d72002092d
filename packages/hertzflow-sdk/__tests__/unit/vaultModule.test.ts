import { describe, it, expect, beforeAll } from '@jest/globals';
import { HertzFlowSDK } from '../../src/sdk';
import { TokenWhitelistItem } from '../../src/modules';
import { COMMON_CONSTS } from '../../src/constants';
import {
  TEST_CONFIG,
  TOKEN_CONSTANTS,
  TEST_AMOUNTS,
  SLIPPAGE_VALUES,
  TEST_SCENARIOS,
  TYPE_ARGUMENTS,
} from '../constants/testConstants';
import {
  initializeTestEnvironment,
  createAddLiquidityParams,
  createRemoveLiquidityParams,
  createSwapParams,
  createPositionParams,
  validateTransactionStructure,
  validateMoveCallCommands,
  logTestStart,
  logTestSuccess,
  logTestFailure,
  getDynamicSwapAmountCases,
  checkSwapLimitsDetailed,
} from '../helpers/testHelpers';
describe('VaultModule Unit Tests', () => {
  let sdk: HertzFlowSDK;
  let btcInfo: TokenWhitelistItem;

  beforeAll(async () => {
    const testEnv = await initializeTestEnvironment();
    sdk = testEnv.sdk;
    btcInfo = testEnv.btcInfo;
  });

  // ===== createAddLiquidityPayload 测试 =====

  it(
    'should create add liquidity payload with correct workflow',
    async () => {
      const testName = '完整的添加流动性工作流程';
      logTestStart(testName);

      try {
        const params = createAddLiquidityParams(
          TEST_AMOUNTS.ADD_LIQUIDITY.MEDIUM,
          TOKEN_CONSTANTS.SUI.DECIMALS,
          SLIPPAGE_VALUES.LOW,
          TYPE_ARGUMENTS.ADD_LIQUIDITY_SUI,
        );

        const updatePriceTx = await sdk.OracleModule.updateWhiteListPrices();
        const finalTx = await sdk.VaultModule.createAddLiquidityPayload(
          params,
          updatePriceTx,
        );

        validateTransactionStructure(finalTx);
        logTestSuccess(testName);
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should handle SUI asset correctly',
    async () => {
      const testName = 'SUI 资产处理';
      logTestStart(testName);

      try {
        const params = createAddLiquidityParams(
          TEST_AMOUNTS.ADD_LIQUIDITY.MEDIUM,
          TOKEN_CONSTANTS.SUI.DECIMALS,
          SLIPPAGE_VALUES.LOW,
          TYPE_ARGUMENTS.ADD_LIQUIDITY_SUI,
        );

        const updatePriceTx = await sdk.OracleModule.updateWhiteListPrices();
        const tx = await sdk.VaultModule.createAddLiquidityPayload(
          params,
          updatePriceTx,
        );

        validateTransactionStructure(tx);
        logTestSuccess(testName);
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should handle custom transaction correctly',
    async () => {
      const testName = '自定义交易处理';
      logTestStart(testName);

      try {
        const params = createAddLiquidityParams(
          TEST_AMOUNTS.ADD_LIQUIDITY.MEDIUM,
          TOKEN_CONSTANTS.SUI.DECIMALS,
          SLIPPAGE_VALUES.LOW,
          TYPE_ARGUMENTS.ADD_LIQUIDITY_SUI,
        );

        const updatePriceTx = await sdk.OracleModule.updateWhiteListPrices();
        const resultTx = await sdk.VaultModule.createAddLiquidityPayload(
          params,
          updatePriceTx,
        );

        expect(resultTx).toBe(updatePriceTx);
        validateTransactionStructure(resultTx);
        logTestSuccess(testName);
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should validate transaction structure and parameters',
    async () => {
      const testName = '交易结构和参数验证';
      logTestStart(testName);

      try {
        const params = createAddLiquidityParams(
          TEST_AMOUNTS.ADD_LIQUIDITY.MEDIUM,
          TOKEN_CONSTANTS.SUI.DECIMALS,
          SLIPPAGE_VALUES.LOW,
          TYPE_ARGUMENTS.ADD_LIQUIDITY_SUI,
        );

        const updatePriceTx = await sdk.OracleModule.updateWhiteListPrices();
        const tx = await sdk.VaultModule.createAddLiquidityPayload(
          params,
          updatePriceTx,
        );

        const txData = validateTransactionStructure(tx);
        validateMoveCallCommands(txData);
        logTestSuccess(testName);
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // ===== 移除流动性测试 =====

  it(
    'should create remove liquidity payload correctly',
    async () => {
      console.log('🚀 开始测试移除流动性交易创建...');

      try {
        const outCoinDecimals = 9; // SUI 的精度是 9

        const removeLiquidityParams = {
          redeemHzlpAmount: '1',
          slippage: 0.01,
          outCoinDecimals: outCoinDecimals,
          typeArguments: ['0x2::sui::SUI'] as [string],
        };

        const updatePriceTx = await sdk.OracleModule.updateWhiteListPrices();
        const finalTx = await sdk.VaultModule.createRemoveLiquidityPayload(
          removeLiquidityParams,
          updatePriceTx,
        );

        expect(finalTx).toBeDefined();
        expect(finalTx.constructor.name).toBe('_Transaction');

        console.log('✅ 移除流动性交易创建测试成功');
      } catch (error) {
        console.error('❌ 移除流动性交易创建测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should handle remove liquidity with SUI asset correctly',
    async () => {
      const testName = '移除流动性 SUI 资产处理';
      logTestStart(testName);

      try {
        const params = createRemoveLiquidityParams(
          TEST_AMOUNTS.REMOVE_LIQUIDITY.MEDIUM,
          SLIPPAGE_VALUES.LOW,
          TOKEN_CONSTANTS.SUI.DECIMALS,
          TYPE_ARGUMENTS.REMOVE_LIQUIDITY_SUI,
        );

        const updatePriceTx = await sdk.OracleModule.updateWhiteListPrices();
        const tx = await sdk.VaultModule.createRemoveLiquidityPayload(
          params,
          updatePriceTx,
        );

        validateTransactionStructure(tx);
        logTestSuccess(testName);
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should handle remove liquidity custom transaction correctly',
    async () => {
      console.log('🚀 开始测试移除流动性自定义交易处理...');
      try {
        const outCoinDecimals = 9; // SUI 的精度是 9
        const params = {
          redeemHzlpAmount: '1',
          slippage: 0.01,
          outCoinDecimals: outCoinDecimals,
          typeArguments: ['0x2::sui::SUI'] as [string],
        };

        const updatePriceTx = await sdk.OracleModule.updateWhiteListPrices();
        const resultTx = await sdk.VaultModule.createRemoveLiquidityPayload(
          params,
          updatePriceTx,
        );

        expect(resultTx).toBe(updatePriceTx);
        expect(resultTx.constructor.name).toBe('_Transaction');

        console.log('✅ 移除流动性自定义交易处理测试通过');
      } catch (error) {
        console.error('❌ 移除流动性自定义交易处理测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should validate remove liquidity transaction structure and parameters',
    async () => {
      const testName = '移除流动性交易结构和参数验证';
      logTestStart(testName);

      try {
        const params = createRemoveLiquidityParams(
          TEST_AMOUNTS.REMOVE_LIQUIDITY.MEDIUM,
          SLIPPAGE_VALUES.LOW,
          TOKEN_CONSTANTS.SUI.DECIMALS,
          TYPE_ARGUMENTS.REMOVE_LIQUIDITY_SUI,
        );

        const updatePriceTx = await sdk.OracleModule.updateWhiteListPrices();
        const tx = await sdk.VaultModule.createRemoveLiquidityPayload(
          params,
          updatePriceTx,
        );

        const txData = validateTransactionStructure(tx);
        validateMoveCallCommands(txData);
        logTestSuccess(testName);
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should handle remove liquidity decimal precision correctly',
    async () => {
      const testName = '移除流动性精度处理';
      logTestStart(testName);

      try {
        const params = createRemoveLiquidityParams(
          TEST_AMOUNTS.REMOVE_LIQUIDITY.MEDIUM,
          SLIPPAGE_VALUES.LOW,
          TOKEN_CONSTANTS.SUI.DECIMALS,
          TYPE_ARGUMENTS.REMOVE_LIQUIDITY_SUI,
        );

        const updatePriceTx = await sdk.OracleModule.updateWhiteListPrices();
        const tx = await sdk.VaultModule.createRemoveLiquidityPayload(
          params,
          updatePriceTx,
        );

        validateTransactionStructure(tx);
        logTestSuccess(testName);
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // ===== createPositionRequestPayload 测试 =====

  it(
    'should create position request payload successfully',
    async () => {
      console.log('🚀 开始测试创建开仓请求载荷...');

      try {
        const params = {
          amountIn: '0.001', // 0.001 SUI
          leverage: 2, // 2倍杠杆
          slippage: 0.01, // 1% 滑点
          isLong: true, // 做多
          typeArguments: [
            '0x2::sui::SUI', // payCoin
            btcInfo.coinType, // collateralCoin
            btcInfo.coinType, // indexCoin
          ] as [string, string, string],
          inCoinMarketPrice: '4.0', // SUI 价格 $4
          inCoinDecimals: 9, // SUI 精度
        };

        const transaction =
          await sdk.VaultModule.createPositionRequestPayload(params);

        // 验证返回的交易对象
        expect(transaction).toBeDefined();
        expect(transaction.getData()).toBeDefined();

        // 验证交易包含正确的合约调用
        const txData = transaction.getData();
        expect(txData.commands).toBeDefined();
        expect(txData.commands.length).toBeGreaterThan(0);

        // 查找 moveCall 命令
        const moveCallCommand = txData.commands.find(
          (cmd: { $kind: string }) => cmd.$kind === 'MoveCall',
        ) as
          | { MoveCall: { function: string; typeArguments: string[] } }
          | undefined;
        expect(moveCallCommand).toBeDefined();
        expect(moveCallCommand?.MoveCall?.function).toBe(
          'create_position_request',
        );

        console.log('✅ 创建开仓请求载荷成功:', {
          commandsCount: txData.commands.length,
          function: moveCallCommand?.MoveCall?.function,
          typeArguments: moveCallCommand?.MoveCall?.typeArguments,
        });
      } catch (error) {
        console.error('❌ 创建开仓请求载荷测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should handle different leverage values correctly',
    async () => {
      const testName = '不同杠杆倍数';
      logTestStart(testName);

      try {
        for (const leverage of TEST_SCENARIOS.LEVERAGE_CASES) {
          console.log(`🔍 测试杠杆: ${leverage}x`);

          const params = createPositionParams(
            TEST_AMOUNTS.POSITION.SMALL,
            leverage,
            SLIPPAGE_VALUES.LOW,
            true, // isLong
            TYPE_ARGUMENTS.POSITION_SUI_BTC(btcInfo.coinType),
            TOKEN_CONSTANTS.SUI.PRICE_USD,
            TOKEN_CONSTANTS.SUI.DECIMALS,
          );

          const transaction =
            await sdk.VaultModule.createPositionRequestPayload(params);
          validateTransactionStructure(transaction);
          console.log(`✅ 杠杆 ${leverage}x 测试成功`);
        }

        logTestSuccess(testName);
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // ===== createSwapPayload 单元测试 =====

  it(
    'should create swap payload successfully with SUI to xBTC',
    async () => {
      const testName = '创建 SUI -> xBTC swap 载荷';
      const params = createSwapParams(
        TEST_AMOUNTS.SWAP.MEDIUM,
        SLIPPAGE_VALUES.LOW,
        TYPE_ARGUMENTS.SWAP_SUI_TO_BTC(btcInfo.coinType),
        TOKEN_CONSTANTS.SUI.DECIMALS,
        btcInfo.coinDecimals, // BTC 精度
      );

      logTestStart(testName, {
        amountIn: params.amountIn,
        slippage: params.slippage,
        fromCoin: 'SUI',
        toCoin: 'xBTC',
      });

      try {
        const transaction = await sdk.VaultModule.createSwapPayload(params);
        const txData = validateTransactionStructure(transaction);
        const moveCallCommands = validateMoveCallCommands(txData);

        // 验证是否包含 swap 函数调用
        const swapCommand = moveCallCommands.find(
          (cmd: { MoveCall?: { function?: string } }) =>
            cmd.MoveCall?.function?.includes('swap'),
        );
        expect(swapCommand).toBeDefined();
        expect(swapCommand?.MoveCall?.typeArguments).toEqual(
          params.typeArguments,
        );

        logTestSuccess(testName, {
          commandsCount: txData.commands.length,
          moveCallCount: moveCallCommands.length,
        });
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should create swap payload with different slippage values',
    async () => {
      const testName = '不同滑点的 swap 载荷创建';
      logTestStart(testName);

      try {
        for (const testCase of TEST_SCENARIOS.SLIPPAGE_CASES) {
          console.log(`🔍 测试: ${testCase.description}`);

          const params = createSwapParams(
            TEST_AMOUNTS.SWAP.MEDIUM,
            testCase.slippage,
            TYPE_ARGUMENTS.SWAP_SUI_TO_BTC(btcInfo.coinType),
            TOKEN_CONSTANTS.SUI.DECIMALS,
            btcInfo.coinDecimals, // BTC 精度
          );

          const transaction = await sdk.VaultModule.createSwapPayload(params);
          validateTransactionStructure(transaction);

          console.log(`✅ ${testCase.description} 测试成功`);
        }

        logTestSuccess(testName);
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should create swap payload with different amounts (dynamic limits)',
    async () => {
      const testName = '不同金额的 swap 载荷创建（动态限制）';
      logTestStart(testName);

      try {
        // 动态获取测试用例
        const dynamicAmountCases = await getDynamicSwapAmountCases(
          sdk,
          COMMON_CONSTS.SUI_TYPE_ARG,
          btcInfo.coinType,
          TOKEN_CONSTANTS.SUI.DECIMALS,
        );

        for (const testCase of dynamicAmountCases) {
          console.log(`🔍 测试: ${testCase.description}`);

          const params = createSwapParams(
            testCase.amount,
            SLIPPAGE_VALUES.LOW,
            TYPE_ARGUMENTS.SWAP_SUI_TO_BTC(btcInfo.coinType),
            TOKEN_CONSTANTS.SUI.DECIMALS,
          );

          const transaction = await sdk.VaultModule.createSwapPayload(params);
          validateTransactionStructure(transaction);

          console.log(`✅ ${testCase.description} 测试成功`);
        }

        logTestSuccess(testName);
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should validate swap payload structure',
    async () => {
      const testName = 'Swap 载荷结构验证';
      logTestStart(testName);

      try {
        const params = createSwapParams(
          TEST_AMOUNTS.SWAP.MEDIUM,
          SLIPPAGE_VALUES.LOW,
          TYPE_ARGUMENTS.SWAP_SUI_TO_BTC(btcInfo.coinType),
          TOKEN_CONSTANTS.SUI.DECIMALS,
          btcInfo.coinDecimals, // BTC 精度
        );

        const transaction = await sdk.VaultModule.createSwapPayload(params);
        const txData = validateTransactionStructure(transaction);
        const moveCallCommands = validateMoveCallCommands(txData);

        // 验证包含必要的命令类型
        const commandTypes = txData.commands.map(
          (cmd: { $kind: string }) => cmd.$kind,
        );
        expect(commandTypes).toContain('MoveCall');

        logTestSuccess(testName, {
          commandTypes,
          moveCallCount: moveCallCommands.length,
        });
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // ===== 反向计算测试 =====

  it(
    'should create swap payload with reverse calculation (amountOut)',
    async () => {
      const testName = '反向计算 Swap 载荷创建 (根据期望输出计算输入)';

      // 使用反向计算参数：期望获得更小的 BTC 数量
      const reverseParams = {
        amountOut: '0.00001', // 期望获得 0.00001 BTC (更小的数量)
        slippage: 0.02,
        typeArguments: [
          '0x2::sui::SUI', // 输入 SUI
          btcInfo.coinType, // 输出 BTC
        ] as [string, string],
        inCoinDecimals: 9, // SUI 精度
        outCoinDecimals: btcInfo.coinDecimals, // BTC 精度
      };

      logTestStart(testName, {
        amountOut: reverseParams.amountOut,
        slippage: reverseParams.slippage,
        fromCoin: 'SUI',
        toCoin: 'BTC',
        calculationType: 'reverse',
      });

      try {
        const transaction = await sdk.VaultModule.createSwapPayload(
          reverseParams,
          undefined,
          { throwOnLimitExceeded: false, verbose: true }, // 禁用限制检查以测试基本功能
        );
        const txData = validateTransactionStructure(transaction);
        const moveCallCommands = validateMoveCallCommands(txData);

        // 验证是否包含 swap 函数调用
        const swapCommand = moveCallCommands.find(
          (cmd: { MoveCall?: { function?: string } }) =>
            cmd.MoveCall?.function?.includes('swap'),
        );
        expect(swapCommand).toBeDefined();
        expect(swapCommand?.MoveCall?.typeArguments).toEqual(
          reverseParams.typeArguments,
        );

        logTestSuccess(testName, {
          commandsCount: txData.commands.length,
          moveCallCount: moveCallCommands.length,
          calculationType: 'reverse',
        });
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should throw error when both amountIn and amountOut are provided',
    async () => {
      const testName = '同时提供 amountIn 和 amountOut 应该抛出错误';

      const invalidParams = {
        amountIn: '1.0', // 不应该同时提供
        amountOut: '0.001', // 不应该同时提供
        slippage: 0.02,
        typeArguments: ['0x2::sui::SUI', btcInfo.coinType] as [string, string],
        inCoinDecimals: 9,
        outCoinDecimals: btcInfo.coinDecimals,
      };

      logTestStart(testName);

      try {
        await expect(
          sdk.VaultModule.createSwapPayload(invalidParams),
        ).rejects.toThrow(
          'Must provide either amountIn (for forward calculation) or amountOut (for reverse calculation), but not both',
        );

        logTestSuccess(testName);
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should throw error when neither amountIn nor amountOut are provided',
    async () => {
      const testName = '既不提供 amountIn 也不提供 amountOut 应该抛出错误';

      const invalidParams = {
        // 既不提供 amountIn 也不提供 amountOut
        slippage: 0.02,
        typeArguments: ['0x2::sui::SUI', btcInfo.coinType] as [string, string],
        inCoinDecimals: 9,
        outCoinDecimals: btcInfo.coinDecimals,
      };

      logTestStart(testName);

      try {
        await expect(
          sdk.VaultModule.createSwapPayload(invalidParams as any),
        ).rejects.toThrow(
          'Must provide either amountIn (for forward calculation) or amountOut (for reverse calculation), but not both',
        );

        logTestSuccess(testName);
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // ===== 限制检查专门测试 =====

  it(
    'should handle swap limit checks correctly',
    async () => {
      const testName = 'Swap 限制检查功能';
      logTestStart(testName);

      try {
        // 测试1: 检查小额交易（应该通过）
        console.log('🔍 测试小额交易限制检查...');
        const smallAmountResult = await checkSwapLimitsDetailed(sdk, {
          fromCoin: COMMON_CONSTS.SUI_TYPE_ARG,
          toCoin: btcInfo.coinType,
          amount: '0.0001',
          fromCoinDecimals: TOKEN_CONSTANTS.SUI.DECIMALS,
        });

        expect(smallAmountResult).toBeDefined();
        expect(smallAmountResult.checkType).toBe('swap');
        console.log('✅ 小额交易检查结果:', smallAmountResult);

        // 测试2: 检查超限交易（应该返回限制信息）
        console.log('🔍 测试超限交易限制检查...');

        // 首先获取实际的最大限制
        const maxSwapIn = await sdk.QueryModule.queryMaxSwapIn({
          coinInType: COMMON_CONSTS.SUI_TYPE_ARG,
          coinOutType: btcInfo.coinType,
        });
        const maxSwapInFormatted =
          Number(maxSwapIn) / Math.pow(10, TOKEN_CONSTANTS.SUI.DECIMALS);
        const superLargeAmount = (maxSwapInFormatted * 2).toString(); // 使用2倍的最大限制

        console.log(
          `📊 最大限制: ${maxSwapInFormatted}, 测试金额: ${superLargeAmount}`,
        );

        const largeAmountResult = await checkSwapLimitsDetailed(sdk, {
          fromCoin: COMMON_CONSTS.SUI_TYPE_ARG,
          toCoin: btcInfo.coinType,
          amount: superLargeAmount, // 真正超限的金额
          fromCoinDecimals: TOKEN_CONSTANTS.SUI.DECIMALS,
        });

        expect(largeAmountResult).toBeDefined();
        expect(largeAmountResult.checkType).toBe('swap');
        expect(largeAmountResult.isValid).toBe(false);
        expect(largeAmountResult.maxLimit).toBeDefined();
        console.log('✅ 超限交易检查结果:', largeAmountResult);

        // 测试3: 验证限制信息的格式
        console.log('🔍 验证限制信息格式...');
        expect(typeof largeAmountResult.maxLimit).toBe('string');
        expect(typeof largeAmountResult.reason).toBe('string');
        expect(typeof largeAmountResult.originalAmount).toBe('string');

        console.log('✅ 限制信息格式验证通过');

        logTestSuccess(testName, {
          smallAmountValid: smallAmountResult.isValid,
          largeAmountValid: largeAmountResult.isValid,
          maxLimit: largeAmountResult.maxLimit,
        });
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should handle limit exceeded errors gracefully',
    async () => {
      const testName = '限制超出错误处理';
      logTestStart(testName);

      try {
        // 测试当throwOnLimitExceeded为true时的行为
        console.log('🔍 测试限制超出时的错误抛出...');

        let errorThrown = false;
        let caughtError: Error | null = null;

        // 首先获取实际的最大限制
        const maxSwapInForError = await sdk.QueryModule.queryMaxSwapIn({
          coinInType: COMMON_CONSTS.SUI_TYPE_ARG,
          coinOutType: btcInfo.coinType,
        });
        const maxSwapInFormattedForError =
          Number(maxSwapInForError) /
          Math.pow(10, TOKEN_CONSTANTS.SUI.DECIMALS);
        const superLargeAmountForError = (
          maxSwapInFormattedForError * 3
        ).toString(); // 使用3倍的最大限制

        console.log(
          `📊 错误测试 - 最大限制: ${maxSwapInFormattedForError}, 测试金额: ${superLargeAmountForError}`,
        );

        try {
          await sdk.VaultModule.limitCheckService.checkSwapLimits(
            {
              coinInType: COMMON_CONSTS.SUI_TYPE_ARG,
              coinOutType: btcInfo.coinType,
              amountIn: superLargeAmountForError, // 真正超限的金额
              inCoinDecimals: TOKEN_CONSTANTS.SUI.DECIMALS,
            },
            {
              throwOnLimitExceeded: true, // 应该抛出错误
            },
          );
        } catch (error) {
          errorThrown = true;
          caughtError = error;
        }

        // 验证错误被正确抛出
        expect(errorThrown).toBe(true);
        expect(caughtError).toBeDefined();

        // 验证错误类型和内容
        if (caughtError && 'errorType' in caughtError) {
          expect((caughtError as any).errorType).toBe('SWAP_LIMIT_EXCEEDED');
        }

        console.log('✅ 限制超出错误正确抛出:', {
          errorType: (caughtError as any)?.errorType || 'unknown',
          message: caughtError?.message || 'no message',
        });

        logTestSuccess(testName, {
          errorThrown,
          errorType: (caughtError as any)?.errorType || 'unknown',
        });
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should provide accurate limit information',
    async () => {
      const testName = '限制信息准确性验证';
      logTestStart(testName);

      try {
        console.log('🔍 获取当前系统限制信息...');

        // 获取限制信息
        const limitInfo = await checkSwapLimitsDetailed(sdk, {
          fromCoin: COMMON_CONSTS.SUI_TYPE_ARG,
          toCoin: btcInfo.coinType,
          amount: '1.0', // 用于获取限制信息
          fromCoinDecimals: TOKEN_CONSTANTS.SUI.DECIMALS,
        });

        console.log('📊 当前限制信息:', limitInfo);

        // 验证限制信息的有效性
        if (
          limitInfo.maxLimit &&
          limitInfo.maxLimit !== 'error' &&
          limitInfo.maxLimit !== 'unknown'
        ) {
          const maxLimitNumber = parseFloat(limitInfo.maxLimit);
          expect(maxLimitNumber).toBeGreaterThan(0);

          console.log(`✅ 最大限制: ${limitInfo.maxLimit} SUI`);

          // 测试在限制范围内的金额
          const safeAmount = (maxLimitNumber * 0.5).toFixed(6);
          console.log(`🔍 测试安全金额: ${safeAmount} SUI`);

          const safeAmountResult = await checkSwapLimitsDetailed(sdk, {
            fromCoin: COMMON_CONSTS.SUI_TYPE_ARG,
            toCoin: btcInfo.coinType,
            amount: safeAmount,
            fromCoinDecimals: TOKEN_CONSTANTS.SUI.DECIMALS,
          });

          expect(safeAmountResult.isValid).toBe(true);
          console.log('✅ 安全金额验证通过');
        }

        logTestSuccess(testName, {
          maxLimit: limitInfo.maxLimit,
          limitInfoComplete: !!limitInfo.maxLimit,
        });
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  it(
    'should skip swap limit check when coinInType equals coinOutType',
    async () => {
      const testName = '相同代币类型跳过Swap限制检查';
      logTestStart(testName);

      try {
        console.log('🔍 测试相同代币类型的限制检查...');

        // 测试相同代币类型（SUI -> SUI）
        const sameTypeResult = await checkSwapLimitsDetailed(sdk, {
          fromCoin: COMMON_CONSTS.SUI_TYPE_ARG,
          toCoin: COMMON_CONSTS.SUI_TYPE_ARG, // 相同类型
          amount: '10.0', // 使用一个很大的金额，如果需要swap会超限
          fromCoinDecimals: TOKEN_CONSTANTS.SUI.DECIMALS,
        });

        // 验证结果
        expect(sameTypeResult).toBeDefined();
        expect(sameTypeResult.isValid).toBe(true);
        expect(sameTypeResult.maxLimit).toBe('no-swap-needed');
        expect(sameTypeResult.checkType).toBe('swap');
        expect(sameTypeResult.reason).toContain('输入输出代币相同');

        console.log('✅ 相同代币类型检查结果:', sameTypeResult);

        // 对比：测试不同代币类型（应该进行实际的限制检查）
        console.log('🔍 对比测试不同代币类型...');

        // 获取实际的最大限制，使用超限金额
        const maxSwapInForDifferent = await sdk.QueryModule.queryMaxSwapIn({
          coinInType: COMMON_CONSTS.SUI_TYPE_ARG,
          coinOutType: btcInfo.coinType,
        });
        const maxSwapInFormattedForDifferent =
          Number(maxSwapInForDifferent) /
          Math.pow(10, TOKEN_CONSTANTS.SUI.DECIMALS);
        const superLargeAmountForDifferent = (
          maxSwapInFormattedForDifferent * 2
        ).toString(); // 使用2倍的最大限制

        console.log(
          `📊 不同代币测试 - 最大限制: ${maxSwapInFormattedForDifferent}, 测试金额: ${superLargeAmountForDifferent}`,
        );

        const differentTypeResult = await checkSwapLimitsDetailed(sdk, {
          fromCoin: COMMON_CONSTS.SUI_TYPE_ARG,
          toCoin: btcInfo.coinType, // 不同类型
          amount: superLargeAmountForDifferent, // 真正超限的金额，这次应该被限制检查拦截
          fromCoinDecimals: TOKEN_CONSTANTS.SUI.DECIMALS,
        });

        expect(differentTypeResult).toBeDefined();
        expect(differentTypeResult.isValid).toBe(false); // 应该被限制检查拦截
        expect(differentTypeResult.maxLimit).not.toBe('no-swap-needed');

        console.log('✅ 不同代币类型检查结果:', differentTypeResult);

        logTestSuccess(testName, {
          sameTypeValid: sameTypeResult.isValid,
          sameTypeMaxLimit: sameTypeResult.maxLimit,
          differentTypeValid: differentTypeResult.isValid,
          differentTypeMaxLimit: differentTypeResult.maxLimit,
        });
      } catch (error) {
        logTestFailure(testName, error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );
});
