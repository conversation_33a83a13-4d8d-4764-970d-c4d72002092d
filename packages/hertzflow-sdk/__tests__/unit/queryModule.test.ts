import { describe, it, expect, beforeAll } from '@jest/globals';
import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';
import { fromHex } from '@mysten/sui/utils';
import { Transaction } from '@mysten/sui/transactions';
import { HertzFlowSDK } from '../../src/sdk';
import { initTestnetSDK } from '../../src/config/testnet';
import { HertzflowError, UtilsErrorCode } from '../../src/errors/errors';
import { COMMON_CONSTS } from '../../src/constants';
import {
  FeeKey,
  SwapWithAmountInQueryParams,
  SwapWithAmountOutQueryParams,
} from '../../src/modules/queryModule';
import { TokenWhitelistItem } from '../../src/modules';
import { TEST_CONFIG } from '../constants/testConstants';

describe('QueryModule Unit Tests', () => {
  let sdk: HertzFlowSDK;
  let keypair: Ed25519Keypair;
  let btcInfo: TokenWhitelistItem;

  beforeAll(async () => {
    console.log('Environment variables:', {
      PRIVATE_KEY: process.env.PRIVATE_KEY ? 'exists' : 'missing',
    });

    const privateKey = process.env.PRIVATE_KEY;
    if (!privateKey)
      throw new HertzflowError(
        'PRIVATE_KEY is not set',
        UtilsErrorCode.InvalidPrivateKeyForTest,
      );
    try {
      if (privateKey.startsWith(COMMON_CONSTS.SUI_PRIVATE_KEY_PREFIX)) {
        keypair = Ed25519Keypair.fromSecretKey(privateKey);
      } else {
        keypair = Ed25519Keypair.fromSecretKey(fromHex(privateKey));
      }
      console.log('senderAddress', keypair.getPublicKey().toSuiAddress());
    } catch (error) {
      console.error('Error creating keypair:', error);
      throw new HertzflowError(
        'Invalid private key',
        UtilsErrorCode.InvalidPrivateKeyForTest,
      );
    }
    sdk = initTestnetSDK();

    // Debug SDK configuration
    console.log('SDK Configuration:', {
      apiUrl: sdk.sdkOptions.apiUrl,
      fullRpcUrl: sdk.sdkOptions.fullRpcUrl,
      senderAddress: sdk.senderAddress,
      hzlpId: sdk.sdkOptions.hzlp.package_id,
    });

    sdk.senderAddress = keypair.getPublicKey().toSuiAddress();
    const tokenWhitelist = await sdk.ApiModule.fetchTokenWhitelist({
      symbol: 'BTC',
    });
    btcInfo = tokenWhitelist[0];
  });

  it('should parse HZLP object using SDK configuration', async () => {
    console.log('🚀 开始测试HZLP对象解析 (使用SDK配置)...');

    try {
      // 使用SDK配置中的HZLP ID
      const hzlpInfo = await sdk.QueryModule.parseHzlpObject();
      // 验证返回的对象信息
      expect(hzlpInfo).toBeDefined();

      console.log('✅ HZLP对象解析测试通过 (使用SDK配置)', hzlpInfo);
    } catch (error) {
      console.error('❌ HZLP对象解析测试失败 (使用SDK配置):', error);
      throw error;
    }
  });

  // ProtocolStore 对象解析测试
  it(
    'should parse ProtocolStore object using SDK configuration',
    async () => {
      console.log('🚀 开始测试ProtocolStore对象解析 (使用SDK配置)...');

      try {
        // 使用SDK配置中的ProtocolStore ID
        const protocolStoreInfo =
          await sdk.QueryModule.parseProtocolStoreObject();

        // 验证返回的对象信息
        expect(protocolStoreInfo).toBeDefined();
        expect(protocolStoreInfo.type).toBeDefined();
        expect(protocolStoreInfo.acl).toBeDefined();
        expect(protocolStoreInfo.feature).toBeDefined();
        expect(protocolStoreInfo.fee).toBeDefined();
        expect(protocolStoreInfo.funding_fee).toBeDefined();
        expect(protocolStoreInfo.id).toBeDefined();
        expect(protocolStoreInfo.white_list).toBeDefined();

        console.log(
          '✅ ProtocolStore对象解析测试通过 (使用SDK配置)',
          protocolStoreInfo,
        );
      } catch (error) {
        console.error('❌ ProtocolStore对象解析测试失败 (使用SDK配置):', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // ===== 费用查询测试 =====

  // 测试获取单个费用类型的费率
  it(
    'should get fee rate for specific fee type',
    async () => {
      console.log('🚀 开始测试获取单个费用类型的费率...');

      try {
        // 测试获取兑换费用费率
        const swapFeeRate = await sdk.QueryModule.getFeeRate(FeeKey.SwapFee);

        expect(swapFeeRate).toBeDefined();
        expect(typeof swapFeeRate).toBe('string');
        expect(Number(swapFeeRate)).toBeGreaterThanOrEqual(0);

        console.log(`✅ 兑换费用费率: ${swapFeeRate} 基点`);

        // 测试获取开仓费用费率
        const increasePositionFeeRate = await sdk.QueryModule.getFeeRate(
          FeeKey.IncreasePositionFee,
        );

        expect(increasePositionFeeRate).toBeDefined();
        expect(typeof increasePositionFeeRate).toBe('string');
        expect(Number(increasePositionFeeRate)).toBeGreaterThanOrEqual(0);

        console.log(`✅ 开仓费用费率: ${increasePositionFeeRate} 基点`);
      } catch (error) {
        console.error('❌ 获取费用类型的费率测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // 测试获取所有费用类型的费率
  it(
    'should get all fee rates',
    async () => {
      console.log('🚀 开始测试获取所有费用类型的费率...');

      try {
        const allFeeRates = await sdk.QueryModule.getAllFeeRates();

        expect(allFeeRates).toBeDefined();
        expect(typeof allFeeRates).toBe('object');

        // 验证包含所有预期的费用类型
        const expectedFeeTypes = [
          'LiquidationFee',
          'TaxFee',
          'StableTaxFee',
          'SwapFee',
          'StableSwapFee',
          'AddRemoveFee',
          'IncreasePositionFee',
          'DecreasePositionFee',
          'ProtocolFee',
        ];

        expectedFeeTypes.forEach((feeType) => {
          expect(allFeeRates[feeType]).toBeDefined();
          expect(typeof allFeeRates[feeType]).toBe('string');
          expect(Number(allFeeRates[feeType])).toBeGreaterThanOrEqual(0);
        });

        console.log('✅ 所有费用类型的费率获取成功:', allFeeRates);
      } catch (error) {
        console.error('❌ 获取所有费用类型的费率测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // 测试费用金额计算

  // 测试 FeeKey 枚举
  it('should have correct FeeKey enum values', () => {
    console.log('🚀 开始测试 FeeKey 枚举值...');

    // 验证枚举值与 Move 合约中的定义一致
    expect(FeeKey.LiquidationFee).toBe(0);
    expect(FeeKey.TaxFee).toBe(1);
    expect(FeeKey.StableTaxFee).toBe(2);
    expect(FeeKey.SwapFee).toBe(3);
    expect(FeeKey.StableSwapFee).toBe(4);
    expect(FeeKey.AddRemoveFee).toBe(5);
    expect(FeeKey.IncreasePositionFee).toBe(6);
    expect(FeeKey.DecreasePositionFee).toBe(7);
    expect(FeeKey.ProtocolFee).toBe(8);

    console.log('✅ FeeKey 枚举值验证通过');
  });

  // ===== 流动性查询测试 =====

  // 测试添加流动性查询
  it(
    'should query add liquidity amount and fee correctly',
    async () => {
      console.log('🚀 开始测试添加流动性查询...');

      try {
        const params = {
          coinType: '0x2::sui::SUI', // 使用 SUI 作为测试代币
          amount: '0.001',
          decimals: 9, // SUI 有 9 位小数
          slippage: 0.01, // 1% 滑点
        };

        const result =
          await sdk.QueryModule.queryAddLiquidityAmountAndFee(params);

        // 验证返回结果结构
        expect(result).toBeDefined();
        expect(result.amountOutAfterFee).toBeDefined();
        expect(result.amountOutAfterFeeWithSlippage).toBeDefined();
        expect(result.fee).toBeDefined();

        // 验证返回值类型
        expect(typeof result.amountOutAfterFee).toBe('string');
        expect(typeof result.amountOutAfterFeeWithSlippage).toBe('string');
        expect(typeof result.fee).toBe('string');

        // 验证数值逻辑
        const amountOutAfterFee = Number(result.amountOutAfterFee);
        const amountOutAfterFeeWithSlippage = Number(
          result.amountOutAfterFeeWithSlippage,
        );
        const fee = Number(result.fee);

        expect(amountOutAfterFee).toBeGreaterThan(0);
        expect(amountOutAfterFeeWithSlippage).toBeGreaterThan(0);
        expect(fee).toBeGreaterThanOrEqual(0);

        // 验证滑点逻辑：考虑滑点后的金额应该小于等于原始金额
        expect(amountOutAfterFeeWithSlippage).toBeLessThanOrEqual(
          amountOutAfterFee,
        );

        console.log('✅ 添加流动性查询结果:', {
          amountOutAfterFee: result.amountOutAfterFee,
          amountOutAfterFeeWithSlippage: result.amountOutAfterFeeWithSlippage,
          fee: result.fee,
        });
      } catch (error) {
        console.error('❌ 添加流动性查询测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // 测试移除流动性查询
  it(
    'should query remove liquidity amount and fee correctly',
    async () => {
      console.log('🚀 开始测试移除流动性查询...');

      try {
        const params = {
          coinType: '0x2::sui::SUI', // 使用 SUI 作为测试代币
          hzlpAmount: '3', // 1 HZLP
          slippage: 0.01, // 1% 滑点
        };

        const result =
          await sdk.QueryModule.queryRemoveLiquidityAmountAndFee(params);
        console.log('queryRemoveLiquidityAmountAndFee result', result);
        expect(result).toBeDefined();
        expect(result.amountOutAfterFee).toBeDefined();
        expect(result.amountOutAfterFeeWithSlippage).toBeDefined();
        expect(result.fee).toBeDefined();

        expect(typeof result.amountOutAfterFee).toBe('string');
        expect(typeof result.amountOutAfterFeeWithSlippage).toBe('string');
        expect(typeof result.fee).toBe('string');

        // 验证数值逻辑
        const amountOutAfterFee = Number(result.amountOutAfterFee);
        const amountOutAfterFeeWithSlippage = Number(
          result.amountOutAfterFeeWithSlippage,
        );
        const fee = Number(result.fee);

        expect(amountOutAfterFee).toBeGreaterThan(0);
        expect(amountOutAfterFeeWithSlippage).toBeGreaterThan(0);
        expect(fee).toBeGreaterThanOrEqual(0);

        expect(amountOutAfterFeeWithSlippage).toBeLessThanOrEqual(
          amountOutAfterFee,
        );

        console.log('✅ 移除流动性查询结果:', {
          amountOutAfterFee: result.amountOutAfterFee,
          amountOutAfterFeeWithSlippage: result.amountOutAfterFeeWithSlippage,
          fee: result.fee,
        });
      } catch (error) {
        console.error('❌ 移除流动性查询测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // ===== Swap 查询测试 =====

  // 测试 Swap 输出金额查询
  it(
    'should query swap amount out correctly',
    async () => {
      console.log('🚀 开始测试 Swap 输出金额查询...');
      try {
        const params: SwapWithAmountInQueryParams = {
          typeArguments: [
            COMMON_CONSTS.SUI_TYPE_ARG,
            btcInfo.coinType, // 输出 BTC (测试网)
          ] as [string, string],
          expectedAmountIn: '0.001', // 0.001 SUI
          inCoinDecimals: 9, // SUI 有 9 位小数
          slippage: 0.01, // 1% 滑点
        };

        const result = await sdk.QueryModule.querySwapAmountOut(params);

        // 验证返回结果结构
        expect(result).toBeDefined();
        expect(result.amountOutAfterSwap).toBeDefined();
        expect(result.amountOutAfterSwapWithSlippage).toBeDefined();
        expect(result.fee).toBeDefined();
        expect(result.feeRateBps).toBeDefined();

        // 验证返回值类型
        expect(typeof result.amountOutAfterSwap).toBe('string');
        expect(typeof result.amountOutAfterSwapWithSlippage).toBe('string');
        expect(typeof result.fee).toBe('string');
        expect(typeof result.feeRateBps).toBe('string');

        // 验证数值逻辑
        const amountOutAfterFee = Number(result.amountOutAfterSwap);
        const amountOutAfterFeeWithSlippage = Number(
          result.amountOutAfterSwapWithSlippage,
        );
        const fee = Number(result.fee);
        const feeRateBps = Number(result.feeRateBps);

        expect(amountOutAfterFee).toBeGreaterThan(0);
        expect(amountOutAfterFeeWithSlippage).toBeGreaterThan(0);
        expect(fee).toBeGreaterThanOrEqual(0);
        expect(feeRateBps).toBeGreaterThanOrEqual(0);
        expect(feeRateBps).toBeLessThanOrEqual(10000); // 费率不应超过 100%

        // 验证滑点逻辑：考虑滑点后的金额应该小于等于原始金额
        expect(amountOutAfterFeeWithSlippage).toBeLessThanOrEqual(
          amountOutAfterFee,
        );

        console.log('✅ Swap 输出金额查询结果:', {
          amountOutAfterFee: result.amountOutAfterSwap,
          amountOutAfterFeeWithSlippage: result.amountOutAfterSwapWithSlippage,
          fee: result.fee,
          feeRateBps: result.feeRateBps,
        });
      } catch (error) {
        console.error('❌ Swap 输出金额查询测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // 测试 Swap 输入金额查询
  it(
    'should query swap amount in correctly',
    async () => {
      console.log('🚀 开始测试 Swap 输入金额查询...');

      try {
        const params: SwapWithAmountOutQueryParams = {
          typeArguments: [
            '0x2::sui::SUI', // 输入 SUI
            btcInfo.coinType, // 输出 BTC (测试网)
          ] as [string, string],
          expectedAmountOut: '100', // 期望输出 0.000001 BTC (很小的金额)
          outCoinDecimals: btcInfo.coinDecimals, // BTC 有 8 位小数
          slippage: 0.01, // 1% 滑点
        };

        const result = await sdk.QueryModule.querySwapAmountIn(params);

        // 验证返回结果结构
        expect(result).toBeDefined();
        expect(result.amountInAfterSwap).toBeDefined();
        expect(result.amountInAfterSwapWithSlippage).toBeDefined();
        expect(result.feeRateBps).toBeDefined();

        // 验证返回值类型
        expect(typeof result.amountInAfterSwap).toBe('string');
        expect(typeof result.amountInAfterSwapWithSlippage).toBe('string');
        expect(typeof result.feeRateBps).toBe('string');

        // 验证数值逻辑
        const amountInAfterFee = Number(result.amountInAfterSwap);
        const amountInAfterFeeWithSlippage = Number(
          result.amountInAfterSwapWithSlippage,
        );
        const feeRateBps = Number(result.feeRateBps);

        expect(amountInAfterFee).toBeGreaterThan(0);
        expect(amountInAfterFeeWithSlippage).toBeGreaterThan(0);
        expect(feeRateBps).toBeGreaterThanOrEqual(0);
        expect(feeRateBps).toBeLessThanOrEqual(10000); // 费率不应超过 100%

        // 验证滑点逻辑：考虑滑点后的输入金额应该大于等于原始金额
        expect(amountInAfterFeeWithSlippage).toBeGreaterThanOrEqual(
          amountInAfterFee,
        );

        console.log('✅ Swap 输入金额查询结果:', {
          amountInAfterFee: result.amountInAfterSwap,
          amountInAfterFeeWithSlippage: result.amountInAfterSwapWithSlippage,
          feeRateBps: result.feeRateBps,
        });
      } catch (error) {
        console.error('❌ Swap 输入金额查询测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // 测试最大 Swap 输入金额查询
  it(
    'should query max swap in correctly',
    async () => {
      console.log('🚀 开始测试最大 Swap 输入金额查询...');

      try {
        const params = {
          coinInType: '0x2::sui::SUI', // 输入 SUI
          coinOutType: btcInfo.coinType, // 输出 BTC (测试网)
        };

        const maxAmountIn = await sdk.QueryModule.queryMaxSwapIn(params);

        // 验证返回结果
        expect(maxAmountIn).toBeDefined();
        expect(typeof maxAmountIn).toBe('string');

        // 验证数值逻辑
        const maxAmount = Number(maxAmountIn);
        expect(maxAmount).toBeGreaterThanOrEqual(0);

        console.log('✅ 最大 Swap 输入金额查询结果:', {
          maxAmountIn: maxAmountIn,
          maxAmountInFormatted: `${maxAmount / 1e9} SUI`, // 假设是 SUI
        });
      } catch (error) {
        console.error('❌ 最大 Swap 输入金额查询测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // 测试相同代币类型的 Swap
  it(
    'should handle same token swap correctly',
    async () => {
      console.log('🚀 开始测试相同代币类型的 Swap...');

      try {
        const sameTokenParams = {
          typeArguments: ['0x2::sui::SUI', '0x2::sui::SUI'] as [string, string],
          expectedAmountIn: '0.001',
          inCoinDecimals: 9,
          slippage: 0.01, // 1% 滑点
        };

        const sameTokenResult =
          await sdk.QueryModule.querySwapAmountOut(sameTokenParams);

        console.log('✅ 相同代币 Swap 结果:', sameTokenResult);
      } catch (error) {
        console.error('❌ 相同代币 Swap 测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // 测试相同代币类型的 Swap AmountIn 查询
  it(
    'should handle same token swap amount in correctly',
    async () => {
      console.log('🚀 开始测试相同代币类型的 Swap AmountIn 查询...');

      try {
        const sameTokenParams = {
          typeArguments: ['0x2::sui::SUI', '0x2::sui::SUI'] as [string, string],
          expectedAmountOut: '0.001',
          outCoinDecimals: 9,
          slippage: 0.01, // 1% 滑点
        };

        const sameTokenResult =
          await sdk.QueryModule.querySwapAmountIn(sameTokenParams);

        // 验证返回结果结构
        expect(sameTokenResult).toBeDefined();
        expect(sameTokenResult.amountInAfterSwap).toBeDefined();
        expect(sameTokenResult.amountInAfterSwapWithSlippage).toBeDefined();
        expect(sameTokenResult.feeRateBps).toBeDefined();

        // 验证相同代币 swap 的特殊逻辑
        expect(sameTokenResult.amountInAfterSwap).toBe(
          sameTokenParams.expectedAmountOut,
        );
        expect(sameTokenResult.feeRateBps).toBe('0');

        console.log('✅ 相同代币 Swap AmountIn 结果:', sameTokenResult);
      } catch (error) {
        console.error('❌ 相同代币 Swap AmountIn 测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // 测试 Swap 查询的边界情况
  it(
    'should handle edge cases for swap queries',
    async () => {
      console.log('🚀 开始测试 Swap 查询边界情况...');

      try {
        // 测试零金额输入
        const zeroAmountParams = {
          typeArguments: ['0x2::sui::SUI', btcInfo.coinType] as [
            string,
            string,
          ],
          expectedAmountIn: '0',
          inCoinDecimals: 9,
          slippage: 0.01,
        };

        try {
          const zeroAmountResult =
            await sdk.QueryModule.querySwapAmountOut(zeroAmountParams);
          console.log('零金额 Swap 结果:', zeroAmountResult);

          // 零金额应该返回零输出
          expect(Number(zeroAmountResult.amountOutAfterSwap)).toBe(0);
        } catch (error) {
          console.log('零金额 Swap 预期失败:', error.message);
          // 零金额 swap 失败也是合理的
        }

        console.log('✅ Swap 查询边界情况测试完成');
      } catch (error) {
        console.error('❌ Swap 查询边界情况测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // ===== TODO:订单查询测试 =====

  // ===== LP价格查询测试 =====

  // 测试AUM和LP价格查询
  it(
    'should query LP price and aum correctly',
    async () => {
      console.log('🚀 开始测试AUM和LP价格查询');

      try {
        const result = await sdk.QueryModule.queryAumAndLpPrice();

        // 验证返回结果结构
        expect(result).toBeDefined();
        expect(result.aum).toBeDefined();
        expect(result.lpPrice).toBeDefined();

        // 验证LP价格数据
        expect(result.lpPrice.lpPriceWithDecimal).toBeDefined();
        expect(typeof result.lpPrice.lpPriceWithDecimal).toBe('string');

        // 验证LP价格是数字格式
        const lpPriceNumber = Number(result.lpPrice.lpPriceWithDecimal);
        expect(lpPriceNumber).toBeGreaterThanOrEqual(0);
        expect(isNaN(lpPriceNumber)).toBe(false);

        // 验证AUM数据
        expect(result.aum.aumWithDecimal).toBeDefined();
        expect(typeof result.aum.aumWithDecimal).toBe('string');
        const aumNumber = Number(result.aum.aumWithDecimal);
        expect(aumNumber).toBeGreaterThanOrEqual(0);
        expect(isNaN(aumNumber)).toBe(false);

        console.log('✅ AUM和LP价格查询结果:', {
          aum: result.aum,
          lpPrice: result.lpPrice,
        });

        // 验证精度格式
        expect(result.lpPrice.lpPriceWithDecimal).toMatch(/^\d+$/); // LP价格应该是整数字符串
        expect(result.aum.aumWithDecimal).toMatch(/^\d+(\.\d+)?$/); // AUM可能包含小数点
      } catch (error) {
        console.error('❌ AUM和LP价格查询测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // 测试LP价格查询错误处理
  it(
    'should handle LP price query errors gracefully',
    async () => {
      console.log('🚀 开始测试LP价格查询错误处理...');

      try {
        // 创建一个使用无效配置的SDK实例来测试错误处理
        const invalidSdk = initTestnetSDK();
        // 修改配置使其无效（但保持基本结构）
        const originalVaultId = invalidSdk.sdkOptions.vault.package_id;
        invalidSdk.sdkOptions.vault.package_id =
          '0x1234567890abcdef1234567890abcdef12345678';

        try {
          await invalidSdk.QueryModule.queryAumAndLpPrice();
          // 如果没有抛出错误，说明测试失败
          expect(true).toBe(false);
        } catch (error) {
          // 验证错误类型和消息
          expect(error).toBeInstanceOf(Error);
          // 错误消息可能包含不同的内容，只要抛出了错误就说明错误处理正常
          expect(error.message).toBeDefined();
          expect(error.message.length).toBeGreaterThan(0);
          console.log('✅ 错误处理验证通过:', error.message);
        } finally {
          // 恢复原始配置
          invalidSdk.sdkOptions.vault.package_id = originalVaultId;
        }
      } catch (error) {
        console.error('❌ LP价格查询错误处理测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // ===== Vault对象解析测试 =====

  // 测试Vault对象解析
  it(
    'should parse vault object correctly',
    async () => {
      console.log('🚀 开始测试Vault对象解析...');

      try {
        const vaultInfo = await sdk.QueryModule.parseVaultObject();

        // 验证返回结果
        expect(vaultInfo).toBeDefined();
        expect(typeof vaultInfo).toBe('object');

        console.log('✅ Vault对象解析结果:', {
          vaultInfo: JSON.stringify(vaultInfo, null, 2),
          objectId: vaultInfo?.objectId,
          version: vaultInfo?.version,
          digest: vaultInfo?.digest,
          type: vaultInfo?.type,
          owner: vaultInfo?.owner,
          content: vaultInfo?.content,
          display: vaultInfo?.display,
        });

        // 基本验证
        if (vaultInfo?.objectId) {
          expect(typeof vaultInfo.objectId).toBe('string');
          expect(vaultInfo.objectId.length).toBeGreaterThan(0);
        }

        if (vaultInfo?.version) {
          expect(typeof vaultInfo.version).toBe('string');
        }

        if (vaultInfo?.type) {
          expect(typeof vaultInfo.type).toBe('string');
          expect(vaultInfo.type).toContain('vault');
        }
      } catch (error) {
        console.error('❌ Vault对象解析测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // 测试Vault对象解析错误处理
  it(
    'should handle vault object parsing errors gracefully',
    async () => {
      console.log('🚀 开始测试Vault对象解析错误处理...');

      try {
        // 创建一个使用无效配置的SDK实例来测试错误处理
        const invalidSdk = initTestnetSDK();
        // 修改配置使其无效
        const originalVaultId = invalidSdk.sdkOptions.vault.package_id;
        invalidSdk.sdkOptions.vault.package_id =
          '0x1234567890abcdef1234567890abcdef12345678';

        try {
          await invalidSdk.QueryModule.parseVaultObject();
          // 如果没有抛出错误，说明测试失败
          expect(true).toBe(false);
        } catch (error) {
          // 验证错误类型和消息
          expect(error).toBeInstanceOf(Error);
          expect(error.message).toBeDefined();
          expect(error.message.length).toBeGreaterThan(0);
          console.log('✅ Vault对象解析错误处理验证通过:', error.message);
        } finally {
          // 恢复原始配置
          invalidSdk.sdkOptions.vault.package_id = originalVaultId;
        }
      } catch (error) {
        console.error('❌ Vault对象解析错误处理测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // ===== getFixedCoinAmount迁移测试 =====

  // 测试getFixedCoinAmount迁移后的功能
  it(
    'should use coinWithBalance for getFixedCoinAmount',
    () => {
      console.log('🚀 开始测试getFixedCoinAmount迁移...');

      try {
        const tx = new Transaction();

        // 测试SUI代币
        const suiCoin = sdk.RpcModule.getFixedCoinAmount({
          txb: tx,
          address: sdk.senderAddress,
          coinType: '0x2::sui::SUI',
          amount: '1000000000', // 1 SUI
        });

        // 验证返回的是交易对象引用（TransactionObjectArgument）
        expect(suiCoin).toBeDefined();
        // coinWithBalance返回的是TransactionObjectArgument，它是一个函数类型
        expect(typeof suiCoin).toBe('function');

        console.log('✅ SUI代币获取成功');

        // 测试其他代币类型
        const otherCoin = sdk.RpcModule.getFixedCoinAmount({
          txb: tx,
          address: sdk.senderAddress,
          coinType: '0x123::usdc::USDC',
          amount: '1000000', // 1 USDC
        });

        expect(otherCoin).toBeDefined();
        expect(typeof otherCoin).toBe('function');

        console.log('✅ 其他代币获取成功');

        // 测试赞助交易模式
        const sponsoredCoin = sdk.RpcModule.getFixedCoinAmount({
          txb: tx,
          address: sdk.senderAddress,
          coinType: '0x2::sui::SUI',
          amount: '1000000000',
          isSponsored: true,
        });

        expect(sponsoredCoin).toBeDefined();
        expect(typeof sponsoredCoin).toBe('function');
        console.log('✅ 赞助交易模式测试成功');

        console.log('✅ getFixedCoinAmount迁移测试完成');
      } catch (error) {
        console.error('❌ getFixedCoinAmount迁移测试失败:', error);
        throw error;
      }
    },
    TEST_CONFIG.TIMEOUT,
  );

  // ===== fetchSignedPrice 新接口测试 =====

  it('should fetch signed prices with coin types array', async () => {
    console.log('🚀 开始测试fetchSignedPrice新接口...');

    try {
      // 测试1: 使用coin_types[]数组
      console.log('🔍 测试使用coin_types[]数组获取价格...');

      const coinTypes = [
        '0x2::sui::SUI',
        '0x48bccdb49d9a29b723172179fa0898340d88ffdcbd563ef27bffdcb96c56689e::btc::BTC',
      ];

      const signedPriceData = await sdk.ApiModule.fetchSignedPrice(coinTypes);

      expect(signedPriceData).toBeDefined();
      expect(signedPriceData.content).toBeDefined();
      expect(Array.isArray(signedPriceData.content)).toBe(true);

      console.log('✅ 获取到签名价格数据:', {
        contentLength: signedPriceData.content.length,
        coinTypes: signedPriceData.content.map((item) => item.coin_type),
      });

      // 验证返回的数据包含请求的代币类型
      const returnedCoinTypes = signedPriceData.content.map(
        (item) => item.coin_type,
      );
      coinTypes.forEach((coinType) => {
        const found = returnedCoinTypes.some(
          (returned) => returned === coinType,
        );
        if (found) {
          console.log(`✅ 找到代币类型: ${coinType}`);
        } else {
          console.log(`⚠️ 未找到代币类型: ${coinType}`);
        }
      });

      console.log('✅ coin_types[]数组测试成功');
    } catch (error) {
      console.error('❌ fetchSignedPrice测试失败:', error);
      throw error;
    }
  }, 30000);

  it('should fetch all signed prices when get_all is true', async () => {
    console.log('🚀 开始测试fetchSignedPrice get_all参数...');

    try {
      // 测试2: 使用get_all=true
      console.log('🔍 测试使用get_all=true获取所有价格...');

      const allSignedPriceData = await sdk.ApiModule.fetchSignedPrice(
        undefined,
        true,
      );

      expect(allSignedPriceData).toBeDefined();
      expect(allSignedPriceData.content).toBeDefined();
      expect(Array.isArray(allSignedPriceData.content)).toBe(true);

      console.log('✅ 获取到所有签名价格数据:', {
        contentLength: allSignedPriceData.content.length,
        coinTypes: allSignedPriceData.content.map((item) => item.coin_type),
      });

      // 验证get_all返回的数据应该比指定coin_types的数据多（或至少相等）
      expect(allSignedPriceData.content.length).toBeGreaterThanOrEqual(1);

      console.log('✅ get_all=true测试成功');
    } catch (error) {
      console.error('❌ fetchSignedPrice get_all测试失败:', error);
      throw error;
    }
  }, 30000);

  it('should throw error when neither coinTypes nor getAll is provided', async () => {
    console.log('🚀 开始测试fetchSignedPrice参数验证...');

    try {
      // 测试3: 参数验证 - 既没有coinTypes也没有getAll
      console.log('🔍 测试参数验证...');

      let errorThrown = false;
      try {
        await sdk.ApiModule.fetchSignedPrice();
      } catch (error) {
        errorThrown = true;
        expect(error.message).toContain(
          'Either coinTypes must be provided or getAll must be true',
        );
        console.log('✅ 正确抛出参数验证错误:', error.message);
      }

      expect(errorThrown).toBe(true);
      console.log('✅ 参数验证测试成功');
    } catch (error) {
      console.error('❌ fetchSignedPrice参数验证测试失败:', error);
      throw error;
    }
  }, 30000);

  it('should maintain backward compatibility with legacy method', async () => {
    console.log('🚀 开始测试fetchSignedPrice向后兼容性...');

    try {
      // 测试4: 向后兼容性
      console.log('🔍 测试向后兼容的legacy方法...');

      const legacyData = await sdk.ApiModule.fetchSignedPriceLegacy(
        '0x2::sui::SUI',
        '0x48bccdb49d9a29b723172179fa0898340d88ffdcbd563ef27bffdcb96c56689e::btc::BTC',
      );

      expect(legacyData).toBeDefined();
      expect(legacyData.content).toBeDefined();
      expect(Array.isArray(legacyData.content)).toBe(true);

      console.log('✅ Legacy方法正常工作:', {
        contentLength: legacyData.content.length,
        coinTypes: legacyData.content.map((item) => item.coin_type),
      });

      console.log('✅ 向后兼容性测试成功');
    } catch (error) {
      console.error('❌ fetchSignedPrice向后兼容性测试失败:', error);
      throw error;
    }
  }, 30000);

  // ===== updateWhiteListPrices 新接口测试 =====

  it('should update specific coin prices', async () => {
    console.log('🚀 开始测试updateWhiteListPrices指定代币更新...');

    try {
      // 测试1: 更新指定代币价格
      console.log('🔍 测试更新SUI和BTC价格...');

      const coinTypes = [
        '0x2::sui::SUI',
        '0x48bccdb49d9a29b723172179fa0898340d88ffdcbd563ef27bffdcb96c56689e::btc::BTC',
      ];

      const tx = await sdk.OracleModule.updateWhiteListPrices(coinTypes);

      expect(tx).toBeDefined();
      expect(tx.constructor.name).toBe('_Transaction');

      console.log('✅ 指定代币价格更新成功');
    } catch (error) {
      console.error('❌ updateWhiteListPrices指定代币测试失败:', error);
      throw error;
    }
  }, 30000);

  it('should update all coin prices', async () => {
    console.log('🚀 开始测试updateWhiteListPrices全部代币更新...');

    try {
      // 测试2: 更新所有代币价格
      console.log('🔍 测试更新所有代币价格...');

      const tx = await sdk.OracleModule.updateWhiteListPrices();

      expect(tx).toBeDefined();
      expect(tx.constructor.name).toBe('_Transaction');

      console.log('✅ 所有代币价格更新成功');
    } catch (error) {
      console.error('❌ updateWhiteListPrices全部代币测试失败:', error);
      throw error;
    }
  }, 30000);

  it('should maintain backward compatibility for updateWhiteListPrices', async () => {
    console.log('🚀 开始测试updateWhiteListPrices向后兼容性...');

    try {
      // 测试3: 向后兼容性 - 无参数调用
      console.log('🔍 测试无参数调用...');

      const tx = await sdk.OracleModule.updateWhiteListPrices();

      expect(tx).toBeDefined();
      expect(tx.constructor.name).toBe('_Transaction');

      console.log('✅ 向后兼容性测试成功');
    } catch (error) {
      console.error('❌ updateWhiteListPrices向后兼容性测试失败:', error);
      throw error;
    }
  }, 30000);

  // ===== queryRemoveLiquidityAmountAndFee 无钱包测试 =====

  it('should test queryRemoveLiquidityAmountAndFee without wallet connection', async () => {
    console.log('🚀 开始测试queryRemoveLiquidityAmountAndFee无钱包调用...');

    try {
      // 测试参数
      const testParams = {
        coinType:
          '0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI',
        hzlpAmount: '1',
        slippage: 0.02,
      };

      console.log('🔍 测试参数:', testParams);

      // 创建一个没有senderAddress的SDK实例来模拟无钱包情况
      // 使用现有SDK的配置，但不设置senderAddress
      const noWalletSdk = new HertzFlowSDK({
        ...sdk.sdkOptions,
      });
      // 确保没有设置senderAddress（模拟无钱包情况）
      noWalletSdk.disconnectWallet();

      console.log('📊 无钱包SDK配置:', {
        senderAddress: noWalletSdk.senderAddress,
        hasWallet: !!noWalletSdk.senderAddress,
      });

      // 尝试调用方法
      const result =
        await noWalletSdk.QueryModule.queryRemoveLiquidityAmountAndFee(
          testParams,
        );

      console.log('✅ 无钱包调用成功:', result);

      expect(result).toBeDefined();
      expect(result.amountOutAfterFee).toBeDefined();
      expect(result.amountOutAfterFeeWithSlippage).toBeDefined();
      expect(result.fee).toBeDefined();
    } catch (error) {
      console.error('❌ 无钱包调用失败:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });

      // 分析错误类型
      if (
        error.message?.includes('senderAddress') ||
        error.message?.includes('sender')
      ) {
        console.log('🔍 错误分析: 缺少senderAddress导致的失败');
      } else if (error.message?.includes('devInspect')) {
        console.log('🔍 错误分析: devInspectTransactionBlock调用失败');
      } else {
        console.log('🔍 错误分析: 其他类型的错误');
      }

      throw error;
    }
  }, 30000);

  it('should test queryRemoveLiquidityAmountAndFee with wallet connection', async () => {
    console.log('🚀 开始测试queryRemoveLiquidityAmountAndFee有钱包调用...');

    try {
      // 测试参数（与无钱包测试相同）
      const testParams = {
        coinType:
          '0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI',
        hzlpAmount: '1',
        slippage: 0.02,
      };

      console.log('🔍 测试参数:', testParams);
      console.log('📊 有钱包SDK配置:', {
        senderAddress: sdk.senderAddress,
        hasWallet: !!sdk.senderAddress,
      });

      // 使用有钱包的SDK调用方法
      const result =
        await sdk.QueryModule.queryRemoveLiquidityAmountAndFee(testParams);

      console.log('✅ 有钱包调用成功:', result);

      expect(result).toBeDefined();
      expect(result.amountOutAfterFee).toBeDefined();
      expect(result.amountOutAfterFeeWithSlippage).toBeDefined();
      expect(result.fee).toBeDefined();
    } catch (error) {
      console.error('❌ 有钱包调用失败:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });

      throw error;
    }
  }, 30000);

  // ===== TODO:Position查询测试 目前需要等待 keeper 执行后才有 position=====
});
