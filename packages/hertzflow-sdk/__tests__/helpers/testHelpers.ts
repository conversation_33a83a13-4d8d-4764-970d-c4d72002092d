/**
 * 测试辅助函数
 * 抽离相同的测试逻辑，提高代码复用性
 */

import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';
import { fromHex } from '@mysten/sui/utils';
import { HertzFlowSDK } from '../../src/sdk';
import { initTestnetSDK } from '../../src/config/testnet';
import { HertzflowError, UtilsErrorCode } from '../../src/errors/errors';
import { COMMON_CONSTS } from '../../src/constants';
import { TestUtils } from '../utils/testUtils';
import { TokenWhitelistItem } from '../../src/modules';
import {
  EXPECTED_ERRORS,
  LIMIT_CHECK_CONFIG,
} from '../constants/testConstants';
import { expect } from '@jest/globals';

// ===== 初始化相关 =====

/**
 * 初始化测试环境
 */
export async function initializeTestEnvironment(): Promise<{
  sdk: HertzFlowSDK;
  keypair: Ed25519Keypair;
  senderAddress: string;
  testUtils?: TestUtils;
  btcInfo: TokenWhitelistItem;
}> {
  console.log('🔧 初始化测试环境...');

  const privateKey = process.env.PRIVATE_KEY;
  if (!privateKey) {
    throw new HertzflowError(
      'PRIVATE_KEY is not set',
      UtilsErrorCode.InvalidPrivateKeyForTest,
    );
  }

  let keypair: Ed25519Keypair;
  try {
    if (privateKey.startsWith(COMMON_CONSTS.SUI_PRIVATE_KEY_PREFIX)) {
      keypair = Ed25519Keypair.fromSecretKey(privateKey);
    } else {
      keypair = Ed25519Keypair.fromSecretKey(fromHex(privateKey));
    }
  } catch (error) {
    throw new HertzflowError(
      'Invalid private key',
      UtilsErrorCode.InvalidPrivateKeyForTest,
    );
  }

  const senderAddress = keypair.getPublicKey().toSuiAddress();
  const sdk = initTestnetSDK();
  sdk.senderAddress = senderAddress;

  // 获取 BTC 信息
  const tokenWhitelist = await sdk.ApiModule.fetchTokenWhitelist({
    symbol: 'BTC',
  });
  const btcInfo = tokenWhitelist[0];

  console.log('📍 测试地址:', senderAddress);
  console.log('🌐 SDK 配置:', {
    apiUrl: sdk.sdkOptions.apiUrl,
    fullRpcUrl: sdk.sdkOptions.fullRpcUrl,
    senderAddress: sdk.senderAddress,
    packageId: sdk.sdkOptions.packageId,
  });

  return { sdk, keypair, senderAddress, btcInfo };
}

/**
 * 初始化集成测试环境（包含 TestUtils）
 */
export async function initializeIntegrationTestEnvironment(): Promise<{
  sdk: HertzFlowSDK;
  keypair: Ed25519Keypair;
  senderAddress: string;
  testUtils: TestUtils;
  btcInfo: TokenWhitelistItem;
}> {
  const { sdk, keypair, senderAddress, btcInfo } =
    await initializeTestEnvironment();
  const testUtils = new TestUtils(sdk.fullClient, keypair);

  // 检查账户余额
  try {
    const balances = await sdk.getOwnerCoinBalances();
    console.log(
      '💰 账户余额:',
      balances.map((b) => ({
        coinType: b.coinType,
        totalBalance: b.totalBalance,
      })),
    );
  } catch (error) {
    console.warn('⚠️ 无法获取账户余额:', error);
  }

  return { sdk, keypair, senderAddress, testUtils, btcInfo };
}

// ===== 参数构造相关 =====

/**
 * 创建添加流动性参数
 */
export function createAddLiquidityParams(
  assetAmount: string,
  assetDecimals: number,
  slippage: number,
  typeArguments: [string],
) {
  return {
    assetAmount,
    assetDecimals,
    slippage,
    typeArguments,
  };
}

/**
 * 创建移除流动性参数
 */
export function createRemoveLiquidityParams(
  redeemHzlpAmount: string,
  slippage: number,
  outCoinDecimals: number,
  typeArguments: [string],
) {
  return {
    redeemHzlpAmount,
    slippage,
    outCoinDecimals,
    typeArguments,
  };
}

/**
 * 创建 Swap 参数 - 正向计算（根据输入数量计算输出）
 */
export function createSwapParams(
  amountIn: string,
  slippage: number,
  typeArguments: [string, string],
  inCoinDecimals: number,
  outCoinDecimals: number = 9, // 默认为 SUI 精度
) {
  return {
    amountIn,
    slippage,
    typeArguments,
    inCoinDecimals,
    outCoinDecimals,
  };
}

/**
 * 创建反向 Swap 参数 - 反向计算（根据期望输出数量计算输入）
 */
export function createReverseSwapParams(
  amountOut: string,
  slippage: number,
  typeArguments: [string, string],
  inCoinDecimals: number,
  outCoinDecimals: number,
) {
  return {
    amountOut,
    slippage,
    typeArguments,
    inCoinDecimals,
    outCoinDecimals,
  };
}

/**
 * 创建开仓参数
 */
export function createPositionParams(
  amountIn: string,
  leverage: number,
  slippage: number,
  isLong: boolean,
  typeArguments: [string, string, string],
  inCoinMarketPrice: string,
  inCoinDecimals: number,
) {
  return {
    amountIn,
    leverage,
    slippage,
    isLong,
    typeArguments,
    inCoinMarketPrice,
    inCoinDecimals,
  };
}

// ===== 验证相关 =====

/**
 * 验证交易基本结构
 */
export function validateTransactionStructure(transaction: any) {
  expect(transaction).toBeDefined();
  expect(transaction.getData()).toBeDefined();

  const txData = transaction.getData();
  expect(txData.commands).toBeDefined();
  expect(Array.isArray(txData.commands)).toBe(true);
  expect(txData.commands.length).toBeGreaterThan(0);

  return txData;
}

/**
 * 验证 MoveCall 命令
 */
export function validateMoveCallCommands(txData: any) {
  const moveCallCommands = txData.commands.filter(
    (cmd: { $kind: string }) => cmd.$kind === 'MoveCall',
  );
  expect(moveCallCommands.length).toBeGreaterThan(0);

  for (const cmd of moveCallCommands) {
    const moveCall = (cmd as any).MoveCall;
    expect(moveCall).toBeDefined();
    expect(moveCall.package).toBeDefined();
    expect(moveCall.module).toBeDefined();
    expect(moveCall.function).toBeDefined();
    expect(moveCall.arguments).toBeDefined();
  }

  return moveCallCommands;
}

/**
 * 查找特定函数的 MoveCall 命令
 */
export function findMoveCallByFunction(txData: any, functionName: string) {
  const moveCallCommands = validateMoveCallCommands(txData);
  return moveCallCommands.find(
    (cmd: any) => cmd.MoveCall?.function === functionName,
  );
}

// ===== 错误处理相关 =====

/**
 * 检查是否为预期的余额错误
 */
export function isExpectedBalanceError(error: Error): boolean {
  return EXPECTED_ERRORS.BALANCE.some((errorMsg) =>
    error.message.includes(errorMsg),
  );
}

/**
 * 检查是否为预期的流动性错误
 */
export function isExpectedLiquidityError(error: Error): boolean {
  return EXPECTED_ERRORS.LIQUIDITY.some((errorMsg) =>
    error.message.includes(errorMsg),
  );
}

/**
 * 检查是否为预期的合约业务逻辑错误
 */
export function isExpectedContractBusinessError(error: Error): boolean {
  return EXPECTED_ERRORS.CONTRACT_BUSINESS.some((errorMsg) =>
    error.message.includes(errorMsg),
  );
}

/**
 * 检查是否为任何预期错误
 */
export function isExpectedError(error: Error): boolean {
  return (
    isExpectedBalanceError(error) ||
    isExpectedLiquidityError(error) ||
    isExpectedContractBusinessError(error)
  );
}

/**
 * 处理集成测试中的预期错误
 */
export function handleExpectedIntegrationError(
  error: Error,
  context: string,
): void {
  if (isExpectedBalanceError(error)) {
    console.warn(`⚠️ ${context}: 余额不足，这是预期的测试环境限制`);
    return;
  }

  if (isExpectedLiquidityError(error)) {
    console.warn(
      `⚠️ ${context}: 流动性不足或池子配置问题，这在测试网环境中是预期的`,
    );
    console.log('💡 说明: 测试网的流动性池可能未完全配置或已达到限制');
    return;
  }

  if (isExpectedContractBusinessError(error)) {
    console.warn(`⚠️ ${context}: 合约业务逻辑限制，这是预期的`);
    return;
  }

  // 如果不是预期错误，重新抛出
  throw error;
}

// ===== 日志相关 =====

/**
 * 记录测试开始
 */
export function logTestStart(testName: string, params?: any): void {
  console.log(`🚀 开始测试: ${testName}`);
  if (params) {
    console.log('📋 测试参数:', params);
  }
}

/**
 * 记录测试成功
 */
export function logTestSuccess(testName: string, details?: any): void {
  console.log(`✅ ${testName} 测试成功`);
  if (details) {
    console.log('📊 详细信息:', details);
  }
}

/**
 * 记录测试失败
 */
export function logTestFailure(testName: string, error: any): void {
  console.error(`❌ ${testName} 测试失败:`, error);
}

// ===== Swap 限制检查相关 =====

/**
 * 检查 swap 金额是否超过最大限制
 */
export async function checkSwapAmountAgainstMaxIn(
  sdk: any,
  coinInType: string,
  coinOutType: string,
  amountIn: string,
  inCoinDecimals: number,
): Promise<{
  isValid: boolean;
  maxAmountIn: string;
  maxAmountInFormatted: string;
  reason?: string;
}> {
  try {
    // 查询最大可输入金额
    const maxAmountInRaw = await sdk.QueryModule.queryMaxSwapIn({
      coinInType,
      coinOutType,
    });

    const maxAmountInFormatted = (
      Number(maxAmountInRaw) / Math.pow(10, inCoinDecimals)
    ).toString();
    const amountInNumber = Number(amountIn);
    const maxAmountInNumber = Number(maxAmountInFormatted);

    const isValid = amountInNumber <= maxAmountInNumber;

    return {
      isValid,
      maxAmountIn: maxAmountInRaw,
      maxAmountInFormatted,
      reason: isValid
        ? undefined
        : `输入金额 ${amountIn} 超过最大限制 ${maxAmountInFormatted}`,
    };
  } catch (error) {
    console.warn('⚠️ 无法查询最大 swap 输入金额:', error);
    return {
      isValid: true, // 如果查询失败，假设有效
      maxAmountIn: '0',
      maxAmountInFormatted: '0',
      reason: '无法查询最大限制',
    };
  }
}

/**
 * 获取适合测试的 swap 金额
 * 基于 get_max_swap_in 的结果动态调整测试金额
 */
export async function getSafeSwapAmount(
  sdk: any,
  coinInType: string,
  coinOutType: string,
  inCoinDecimals: number,
  preferredAmount?: string,
): Promise<{
  amount: string;
  maxLimit: string;
  isLimitReached: boolean;
  reason: string;
}> {
  try {
    const maxAmountInRaw = await sdk.QueryModule.queryMaxSwapIn({
      coinInType,
      coinOutType,
    });

    const maxAmountInFormatted =
      Number(maxAmountInRaw) / Math.pow(10, inCoinDecimals);

    // 如果最大限制为 0
    if (Number(maxAmountInRaw) === 0) {
      return {
        amount: '0',
        maxLimit: '0',
        isLimitReached: true,
        reason: '最大 swap 输入为 0，无法进行 swap 操作',
      };
    }

    // 计算安全的测试金额（使用安全边距）
    const safeAmount =
      maxAmountInFormatted * LIMIT_CHECK_CONFIG.SWAP.SAFETY_MARGIN;

    // 格式化为合理的小数位数，避免精度问题
    const formattedSafeAmount = Number(safeAmount.toFixed(8)).toString();

    // 如果指定了首选金额，检查是否在安全范围内
    if (preferredAmount) {
      const preferredAmountNumber = Number(preferredAmount);
      if (preferredAmountNumber <= safeAmount) {
        return {
          amount: preferredAmount,
          maxLimit: maxAmountInFormatted.toString(),
          isLimitReached: false,
          reason: `使用首选金额 ${preferredAmount}，在安全范围内`,
        };
      }
    }

    return {
      amount: formattedSafeAmount,
      maxLimit: maxAmountInFormatted.toString(),
      isLimitReached: false,
      reason: `使用安全金额 ${formattedSafeAmount}（最大限制的 ${LIMIT_CHECK_CONFIG.SWAP.SAFETY_MARGIN * 100}%）`,
    };
  } catch (error) {
    console.warn('⚠️ 无法查询最大 swap 输入限制:', error);

    // 如果查询失败，使用默认的小金额
    const fallbackAmount = preferredAmount || '0.0001';
    return {
      amount: fallbackAmount,
      maxLimit: 'unknown',
      isLimitReached: false,
      reason: `查询失败，使用默认金额 ${fallbackAmount}`,
    };
  }
}

/**
 * 分析 swap 失败的原因
 */
export function analyzeSwapFailureReason(
  error: Error,
  maxSwapIn: string,
  attemptedAmount: string,
): string {
  const errorMessage = error.message;

  // 检查是否是流动性不足错误
  if (EXPECTED_ERRORS.LIQUIDITY.some((msg) => errorMessage.includes(msg))) {
    if (Number(maxSwapIn) === 0) {
      return (
        '流动性池状态问题：' +
        LIMIT_CHECK_CONFIG.SWAP.ZERO_MAX_INPUT_REASONS.join('；')
      );
    }
    return `流动性不足：尝试 swap ${attemptedAmount}，但可能超出了实际可用流动性`;
  }

  // 检查是否是余额不足错误
  if (EXPECTED_ERRORS.BALANCE.some((msg) => errorMessage.includes(msg))) {
    return '账户余额不足，无法完成 swap 操作';
  }

  // 检查是否是合约业务逻辑错误
  if (
    EXPECTED_ERRORS.CONTRACT_BUSINESS.some((msg) => errorMessage.includes(msg))
  ) {
    return '合约业务逻辑限制：' + errorMessage;
  }

  return `未知错误：${errorMessage}`;
}

/**
 * 检查是否为 swap 限制相关的预期错误
 */
export function isExpectedSwapLimitError(error: Error): boolean {
  return EXPECTED_ERRORS.SWAP_LIMITS.some((errorMsg) =>
    error.message.includes(errorMsg),
  );
}

/**
 * 检查是否为添加流动性限制相关的预期错误
 */
export function isExpectedAddLiquidityLimitError(error: Error): boolean {
  return EXPECTED_ERRORS.ADD_LIQUIDITY_LIMITS.some((errorMsg) =>
    error.message.includes(errorMsg),
  );
}

/**
 * 检查是否为开仓限制相关的预期错误
 */
export function isExpectedPositionLimitError(error: Error): boolean {
  return EXPECTED_ERRORS.POSITION_LIMITS.some((errorMsg) =>
    error.message.includes(errorMsg),
  );
}

/**
 * 获取适合测试的添加流动性金额
 * 基于池子的 max_usd_amount 限制动态调整测试金额
 */
export async function getSafeAddLiquidityAmount(
  sdk: any,
  coinType: string,
  coinDecimals: number,
  preferredAmount?: string,
): Promise<{
  amount: string;
  maxLimit: string;
  isLimitReached: boolean;
  reason: string;
}> {
  try {
    // 使用现有的查询函数来检测限制
    const testAmount = preferredAmount || '0.001';

    try {
      await sdk.QueryModule.queryAddLiquidityAmountAndFee({
        coinType,
        amount: testAmount,
        decimals: coinDecimals,
        slippage: 0.01,
      });

      // 如果查询成功，说明金额在限制范围内
      return {
        amount: testAmount,
        maxLimit: 'unknown',
        isLimitReached: false,
        reason: `使用测试金额 ${testAmount}，在限制范围内`,
      };
    } catch (error) {
      // 如果查询失败，可能是超过了限制
      if (isExpectedAddLiquidityLimitError(error as Error)) {
        // 尝试使用更小的金额
        const smallerAmount = (
          Number(testAmount) * LIMIT_CHECK_CONFIG.ADD_LIQUIDITY.SAFETY_MARGIN
        ).toString();

        try {
          await sdk.QueryModule.queryAddLiquidityAmountAndFee({
            coinType,
            amount: smallerAmount,
            decimals: coinDecimals,
            slippage: 0.01,
          });

          return {
            amount: smallerAmount,
            maxLimit: 'estimated',
            isLimitReached: false,
            reason: `使用调整后的安全金额 ${smallerAmount}`,
          };
        } catch (innerError) {
          return {
            amount: '0',
            maxLimit: '0',
            isLimitReached: true,
            reason: '池子已达到最大 USD 限制，无法添加流动性',
          };
        }
      }

      throw error; // 重新抛出非预期错误
    }
  } catch (error) {
    console.warn('⚠️ 无法查询添加流动性限制:', error);

    const fallbackAmount = preferredAmount || '0.0001';
    return {
      amount: fallbackAmount,
      maxLimit: 'unknown',
      isLimitReached: false,
      reason: `查询失败，使用默认金额 ${fallbackAmount}`,
    };
  }
}

/**
 * 检查开仓操作是否需要 swap
 */
export function needsSwapForPosition(
  payCoinType: string,
  collateralCoinType: string,
): boolean {
  return payCoinType !== collateralCoinType;
}

/**
 * 获取适合测试的开仓金额
 * 如果需要 swap，会先检查 swap 限制
 */
export async function getSafePositionAmount(
  sdk: any,
  payCoinType: string,
  collateralCoinType: string,
  payCoinDecimals: number,
  preferredAmount?: string,
): Promise<{
  amount: string;
  maxLimit: string;
  isLimitReached: boolean;
  reason: string;
  needsSwap: boolean;
}> {
  const needsSwap = needsSwapForPosition(payCoinType, collateralCoinType);

  try {
    // 如果需要 swap，先检查 swap 限制
    if (needsSwap) {
      const swapLimitInfo = await getSafeSwapAmount(
        sdk,
        payCoinType,
        collateralCoinType,
        payCoinDecimals,
        preferredAmount,
      );

      if (swapLimitInfo.isLimitReached) {
        return {
          amount: swapLimitInfo.amount,
          maxLimit: swapLimitInfo.maxLimit,
          isLimitReached: true,
          reason: `开仓需要 swap，但受到 swap 限制: ${swapLimitInfo.reason}`,
          needsSwap: true,
        };
      }

      return {
        amount: swapLimitInfo.amount,
        maxLimit: swapLimitInfo.maxLimit,
        isLimitReached: false,
        reason: `开仓需要 swap，使用安全的 swap 金额: ${swapLimitInfo.reason}`,
        needsSwap: true,
      };
    }

    // 如果不需要 swap，使用首选金额或默认金额
    const testAmount = preferredAmount || '0.001';
    return {
      amount: testAmount,
      maxLimit: 'no-limit',
      isLimitReached: false,
      reason: `开仓不需要 swap，使用测试金额 ${testAmount}`,
      needsSwap: false,
    };
  } catch (error) {
    console.warn('⚠️ 无法查询开仓限制:', error);

    const fallbackAmount = preferredAmount || '0.0001';
    return {
      amount: fallbackAmount,
      maxLimit: 'unknown',
      isLimitReached: false,
      reason: `查询失败，使用默认金额 ${fallbackAmount}`,
      needsSwap,
    };
  }
}

// ===== 通用限制检查接口 =====

/**
 * 限制检查选项
 */
export interface LimitCheckOptions {
  enableLimitCheck?: boolean;
  throwOnLimitExceeded?: boolean;
}

/**
 * 限制检查结果
 */
export interface LimitCheckResult {
  isValid: boolean;
  adjustedAmount?: string;
  maxLimit: string;
  reason: string;
  originalAmount: string;
}

/**
 * 通用限制检查函数
 */
export async function performLimitCheck(
  checkType: 'swap' | 'addLiquidity' | 'position',
  sdk: any,
  params: any,
  options: LimitCheckOptions = {},
): Promise<LimitCheckResult> {
  const config = {
    enableLimitCheck:
      options.enableLimitCheck ??
      LIMIT_CHECK_CONFIG.COMMON.ENABLE_LIMIT_CHECK_BY_DEFAULT,
    throwOnLimitExceeded:
      options.throwOnLimitExceeded ??
      LIMIT_CHECK_CONFIG.COMMON.THROW_ON_LIMIT_EXCEEDED,
  };

  if (!config.enableLimitCheck) {
    return {
      isValid: true,
      maxLimit: 'not-checked',
      reason: '限制检查已禁用',
      originalAmount: params.amount || params.amountIn || '0',
    };
  }

  try {
    switch (checkType) {
      case 'swap':
        return await performSwapLimitCheck(sdk, params, options);
      case 'addLiquidity':
        return await performAddLiquidityLimitCheck(sdk, params, options);
      case 'position':
        return await performPositionLimitCheck(sdk, params, options);
      default:
        throw new Error(`不支持的检查类型: ${checkType}`);
    }
  } catch (error) {
    if (config.throwOnLimitExceeded) {
      throw error;
    }

    return {
      isValid: false,
      maxLimit: 'error',
      reason: `限制检查失败: ${(error as Error).message}`,
      originalAmount: params.amount || params.amountIn || '0',
    };
  }
}

/**
 * Swap 限制检查实现
 */
async function performSwapLimitCheck(
  sdk: any,
  params: any,
  options: LimitCheckOptions,
): Promise<LimitCheckResult> {
  const safeAmountInfo = await getSafeSwapAmount(
    sdk,
    params.typeArguments[0],
    params.typeArguments[1],
    params.inCoinDecimals,
    params.amountIn,
  );

  if (safeAmountInfo.isLimitReached) {
    return {
      isValid: false,
      maxLimit: safeAmountInfo.maxLimit,
      reason: safeAmountInfo.reason,
      originalAmount: params.amountIn,
    };
  }

  return {
    isValid: true,
    maxLimit: safeAmountInfo.maxLimit,
    reason: safeAmountInfo.reason,
    originalAmount: params.amountIn,
  };
}

/**
 * 添加流动性限制检查实现
 */
async function performAddLiquidityLimitCheck(
  sdk: any,
  params: any,
  options: LimitCheckOptions,
): Promise<LimitCheckResult> {
  const safeAmountInfo = await getSafeAddLiquidityAmount(
    sdk,
    params.typeArguments[0],
    params.assetDecimals,
    params.assetAmount,
  );

  if (safeAmountInfo.isLimitReached) {
    return {
      isValid: false,
      maxLimit: safeAmountInfo.maxLimit,
      reason: safeAmountInfo.reason,
      originalAmount: params.assetAmount,
    };
  }

  return {
    isValid: true,
    maxLimit: safeAmountInfo.maxLimit,
    reason: safeAmountInfo.reason,
    originalAmount: params.assetAmount,
  };
}

/**
 * 开仓限制检查实现
 */
async function performPositionLimitCheck(
  sdk: any,
  params: any,
  options: LimitCheckOptions,
): Promise<LimitCheckResult> {
  const safeAmountInfo = await getSafePositionAmount(
    sdk,
    params.typeArguments[0], // payCoinType
    params.typeArguments[1], // collateralCoinType
    params.inCoinDecimals,
    params.amountIn,
  );

  if (safeAmountInfo.isLimitReached) {
    return {
      isValid: false,
      maxLimit: safeAmountInfo.maxLimit,
      reason: safeAmountInfo.reason,
      originalAmount: params.amountIn,
    };
  }

  return {
    isValid: true,
    maxLimit: safeAmountInfo.maxLimit,
    reason: safeAmountInfo.reason,
    originalAmount: params.amountIn,
  };
}

/**
 * 智能 swap 测试执行器
 * 自动查询最大限制并选择合适的测试金额
 */
export async function executeSmartSwapTest(
  sdk: any,
  testUtils: any,
  coinInType: string,
  coinOutType: string,
  inCoinDecimals: number,
  slippage: number = 0.02,
  testName: string = 'Smart Swap Test',
): Promise<{
  success: boolean;
  txResponse?: any;
  reason: string;
  amountUsed: string;
  maxLimit: string;
}> {
  logTestStart(testName);

  try {
    // 1. 获取安全的测试金额
    const safeAmountInfo = await getSafeSwapAmount(
      sdk,
      coinInType,
      coinOutType,
      inCoinDecimals,
    );

    console.log('📊 智能金额选择结果:', {
      amount: safeAmountInfo.amount,
      maxLimit: safeAmountInfo.maxLimit,
      isLimitReached: safeAmountInfo.isLimitReached,
      reason: safeAmountInfo.reason,
    });

    // 2. 如果达到限制，直接返回
    if (safeAmountInfo.isLimitReached) {
      console.warn('⚠️ 无法执行 swap 测试:', safeAmountInfo.reason);
      return {
        success: false,
        reason: safeAmountInfo.reason,
        amountUsed: safeAmountInfo.amount,
        maxLimit: safeAmountInfo.maxLimit,
      };
    }

    // 3. 创建 swap 参数
    const swapParams = createSwapParams(
      safeAmountInfo.amount,
      slippage,
      [coinInType, coinOutType] as [string, string],
      inCoinDecimals,
      9, // 默认输出精度，实际应该根据输出代币类型确定
    );

    // 4. 先更新价格
    const priceUpdateTx = await sdk.OracleModule.updateWhiteListPrices();

    // 5. 创建 swap 载荷
    const swapTx = await sdk.VaultModule.createSwapPayload(
      swapParams,
      priceUpdateTx,
    );

    // 6. 执行交易
    const txResponse = await testUtils.executeAndWaitForTx(swapTx);

    if (txResponse.effects?.status?.status === 'success') {
      logTestSuccess(testName, {
        amountUsed: safeAmountInfo.amount,
        maxLimit: safeAmountInfo.maxLimit,
        digest: txResponse.digest,
      });

      return {
        success: true,
        txResponse,
        reason: '交易执行成功',
        amountUsed: safeAmountInfo.amount,
        maxLimit: safeAmountInfo.maxLimit,
      };
    } else {
      const errorMsg = txResponse.effects?.status?.error || '未知错误';
      const analysisReason = analyzeSwapFailureReason(
        new Error(errorMsg),
        safeAmountInfo.maxLimit,
        safeAmountInfo.amount,
      );

      console.log('⚠️ Swap 交易失败:', {
        error: errorMsg,
        analysis: analysisReason,
      });

      return {
        success: false,
        txResponse,
        reason: analysisReason,
        amountUsed: safeAmountInfo.amount,
        maxLimit: safeAmountInfo.maxLimit,
      };
    }
  } catch (error) {
    const analysisReason = analyzeSwapFailureReason(error as Error, '0', '0');

    logTestFailure(testName, error);

    // 检查是否是预期错误
    if (isExpectedError(error as Error)) {
      console.log('💡 这是预期的错误，测试通过');
      return {
        success: false,
        reason: `预期错误: ${analysisReason}`,
        amountUsed: '0',
        maxLimit: '0',
      };
    }

    return {
      success: false,
      reason: `意外错误: ${analysisReason}`,
      amountUsed: '0',
      maxLimit: '0',
    };
  }
}

// ===== 动态限制获取相关 =====

/**
 * 动态获取Swap限制并生成测试用例
 */
export async function getDynamicSwapAmountCases(
  sdk: any,
  fromCoinType: string,
  toCoinType: string,
  fromCoinDecimals: number = 9,
): Promise<Array<{ amount: string; description: string }>> {
  try {
    console.log('🔍 获取动态Swap限制...');

    // 使用LimitCheckService获取实际限制
    const limitResult = await sdk.VaultModule.limitCheckService.checkSwapLimits(
      {
        coinInType: fromCoinType,
        coinOutType: toCoinType,
        amountIn: '1.0', // 使用一个较大的测试金额
        inCoinDecimals: fromCoinDecimals,
      },
      {
        throwOnLimitExceeded: false, // 不抛出异常，只返回结果
      },
    );

    let maxLimit = '0.001'; // 默认值

    if (
      limitResult.maxLimit &&
      limitResult.maxLimit !== 'error' &&
      limitResult.maxLimit !== 'unknown' &&
      limitResult.maxLimit !== 'not-checked'
    ) {
      maxLimit = limitResult.maxLimit;
    }

    const maxLimitNumber = parseFloat(maxLimit);
    console.log(`✅ 获取到最大限制: ${maxLimit}`);

    // 生成动态测试用例
    const dynamicCases = [
      {
        amount: '0.0001',
        description: '0.0001 SUI (小额)',
      },
      {
        amount: '0.001',
        description: '0.001 SUI (中等)',
      },
      {
        amount: (maxLimitNumber * 0.8).toFixed(6),
        description: `${(maxLimitNumber * 0.8).toFixed(6)} SUI (大额，限制的80%)`,
      },
    ];

    console.log('✅ 生成动态测试用例:', dynamicCases);
    return dynamicCases;
  } catch (error) {
    console.warn('⚠️ 获取动态限制失败，使用默认测试用例:', error);

    // 如果获取限制失败，返回安全的默认值
    return [
      { amount: '0.0001', description: '0.0001 SUI (小额)' },
      { amount: '0.001', description: '0.001 SUI (中等)' },
      { amount: '0.002', description: '0.002 SUI (大额，保守估计)' },
    ];
  }
}

/**
 * 检查 Swap 限制并返回详细结果
 */
export async function checkSwapLimitsDetailed(
  sdk: any,
  params: {
    fromCoin: string;
    toCoin: string;
    amount: string;
    fromCoinDecimals?: number;
  },
): Promise<any> {
  try {
    return await sdk.VaultModule.limitCheckService.checkSwapLimits(
      {
        coinInType: params.fromCoin,
        coinOutType: params.toCoin,
        amountIn: params.amount,
        inCoinDecimals: params.fromCoinDecimals || 9,
      },
      {
        throwOnLimitExceeded: false, // 不抛出异常，返回详细结果
      },
    );
  } catch (error) {
    console.log('🔍 Swap 限制检查结果:', error);
    throw error;
  }
}
