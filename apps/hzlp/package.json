{"name": "hzlp", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "lingui extract --clean && next dev --turbopack --port 3003", "debug": "NODE_OPTIONS='--inspect' next dev", "build": "lingui extract --clean && next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@hertzflow/sdk": "workspace:*", "@hookform/resolvers": "^4.1.3", "@lingui/core": "^5.2.0", "@lingui/react": "^5.2.0", "@mysten/dapp-kit": "0.16.6", "@mysten/sui": "1.30.2", "@repo/components": "workspace:*", "@repo/i18n": "workspace:*", "@repo/lib": "workspace:*", "@repo/ui": "workspace:*", "@tanstack/react-query": "^5.67.1", "@tanstack/react-table": "^8.21.3", "bignumber.js": "^9.1.2", "date-fns": "^4.1.0", "lodash-es": "^4.17.21", "negotiator": "^1.0.0", "next": "15.2.0", "next-international": "^1.3.1", "next-themes": "^0.4.6", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-responsive": "^10.0.1", "recharts": "3.0.2", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.9", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@lingui/cli": "^5.2.0", "@lingui/conf": "^5.2.0", "@lingui/loader": "^5.2.0", "@lingui/swc-plugin": "^5.4.0", "@repo/configs": "workspace:^", "@types/negotiator": "^0.6.3", "@types/lodash-es": "^4.17.12"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --fix --config ./eslint.config.js", "*.{css,less,scss}": "stylelint --allow-empty-input --config ./stylelint.config.js"}}