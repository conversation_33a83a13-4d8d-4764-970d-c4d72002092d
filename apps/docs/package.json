{"name": "docs", "version": "0.1.0", "scripts": {"docusaurus": "<PERSON>cusaurus", "dev": "TINA_PUBLIC_IS_LOCAL=true tinacms dev -c \"docusaurus start\" ", "start": "docusaurus start", "build": "tinacms build && docusaurus build", "build-local": "tinacms build --local --skip-indexing --skip-cloud-checks && docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "dependencies": {"@docusaurus/core": "3.7.0", "@docusaurus/plugin-content-docs": "3.7.0", "@docusaurus/preset-classic": "3.7.0", "@docusaurus/theme-classic": "3.7.0", "@docusaurus/theme-search-algolia": "3.7.0", "@easyops-cn/docusaurus-search-local": "^0.49.2", "@mdx-js/react": "^3.1.0", "@tinacms/cli": "^1.9.3", "clsx": "^2.1.1", "docusaurus-plugin-openapi-docs": "^4.3.7", "docusaurus-theme-openapi-docs": "^4.3.7", "prism-react-renderer": "^2.4.1", "react": "^18.3.1", "react-dom": "^18.3.1", "tinacms": "^2.7.3", "title": "^3.5.3"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.7.0", "@docusaurus/types": "^3.7.0", "@repo/configs": "workspace:*", "@tailwindcss/postcss": "^4.0.9", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^4.0.11"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18.0"}}