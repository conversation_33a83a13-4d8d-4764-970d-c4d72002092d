---
title: 教程介绍
description: 探索Docusaurus
tags:
  - 入门指南
---

# 教程介绍

让我们在**5分钟内**了解**Docusaurus**。

## 开始使用

通过**创建一个新站点**开始使用。

立即通过\*\*[docusaurus.new](https://docusaurus.new)\*\*体验Docusaurus。

### 所需环境

- [Node.js](https://nodejs.org/en/download/) 版本16.14或更高:
  - 安装Node.js时，建议勾选所有与依赖相关的复选框。

## 创建新站点

使用**经典模板**创建一个新的Docusaurus站点。

运行以下命令后，经典模板将自动添加到您的项目中:

```bash
npm init docusaurus@latest my-website classic
```

您可以在命令提示符、PowerShell、终端或代码编辑器的任何集成终端中输入此命令。

该命令还会安装运行Docusaurus所需的所有依赖项。

## 启动站点

运行开发服务器:

```bash
cd my-website
npm run start
```

`cd`命令用于切换工作目录。要操作新创建的Docusaurus站点，您需要将终端导航到项目目录。

`npm run start`命令会在本地构建您的网站并通过开发服务器运行，您可以在[http://localhost:3000/查看。](http://localhost:3000/%E6%9F%A5%E7%9C%8B%E3%80%82)

打开`docs/intro.md`(本页面)并编辑一些内容: 网站会**自动重新加载**并显示您的更改。
