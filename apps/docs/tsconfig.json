{"$schema": "https://json.schemastore.org/tsconfig", "display": "Docusaurus", "docs": "https://docusaurus.io/docs/typescript-support", "compilerOptions": {"allowJs": true, "esModuleInterop": true, "jsx": "preserve", "target": "ES2022", "lib": ["ES2022", "DOM"], "moduleResolution": "nodenext", "module": "NodeNext", "resolveJsonModule": true, "noEmit": true, "baseUrl": ".", "paths": {"@site/*": ["./*"]}, "skipLibCheck": true}, "exclude": [".docusaurus", "build"]}