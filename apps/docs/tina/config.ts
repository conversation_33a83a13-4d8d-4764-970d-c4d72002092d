import { defineConfig } from "tinacms";

import { DocsCollection, ZhDocsCollection } from "./collections/docsCollection";
import { PagesCollection } from "./collections/pagesCollection";
import { HomepageCollection } from "./collections/homepageCollection";
import { SidebarCollection } from "./collections/sidebarCollection";
import { SettingsCollection } from "./collections/settingsCollection";

export default defineConfig({
  clientId: process.env.NEXT_PUBLIC_TINA_CLIENT_ID, // Get this from tina.io
  token: process.env.TINA_TOKEN, // Get this from tina.io
  branch:
    process.env.GITHUB_BRANCH || process.env.VERCEL_GIT_COMMIT_REF || "main",
  build: {
    outputFolder: "admin",
    publicFolder: "static",
  },
  media: {
    tina: {
      mediaRoot: "img",
      publicFolder: "static",
    },
  },
  schema: {
    collections: [
      DocsCollection,
      ZhDocsCollection,
      HomepageCollection,
      PagesCollection,
      SidebarCollection,
      SettingsCollection,
    ],
  },
});
