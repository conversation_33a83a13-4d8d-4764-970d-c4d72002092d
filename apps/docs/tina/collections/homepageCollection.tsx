import React from "react";
import { FeaturesBlockTemplate } from "../../src/components/Features/template";
import { HeroBlockTemplate } from "../../src/components/Hero/template";
import { YouTubeEmbedBlockTemplate } from "../../src/components/YouTubeEmbed/template";
import RestartWarning from "../components/RestartWarning";

export const HomepageCollection = {
  name: "homepage",
  label: "Homepage",
  description:
    "To see settings changes reflected on your site, you must restart the Tina CLI after saving changes (local development only).",
  path: "config/homepage",
  format: "json",
  ui: {
    allowedActions: {
      create: false,
      delete: false,
    },
  },
  fields: [
    {
      type: "string",
      name: "_warning",
      ui: {
        component: () => {
          return <RestartWarning />;
        },
      },
    },
    {
      type: "string",
      label: "Label",
      name: "label",
      required: true,
      isTitle: true,
      ui: {
        component: "hidden",
      },
    },
    {
      type: "string",
      name: "title",
      label: "Title",
    },
    {
      type: "string",
      name: "description",
      label: "Description",
    },
    {
      type: "object",
      list: true,
      name: "blocks",
      label: "Blocks",
      templates: [
        HeroBlockTemplate,
        FeaturesBlockTemplate,
        YouTubeEmbedBlockTemplate,
      ],
    },
  ],
};
