import { match } from "assert";
import { MDXTemplates } from "../../src/theme/template";

const docsFields = [
  {
    type: "string",
    name: "title",
    label: "Title",
    isTitle: true,
    required: true,
  },
  {
    type: "string",
    name: "description",
    label: "Description",
  },
  {
    label: "Tags",
    name: "tags",
    type: "string",
    list: true,
    ui: {
      component: "tags",
    },
  },
  {
    type: "rich-text",
    name: "body",
    label: "Body",
    isBody: true,
    templates: [...MDXTemplates],
  },
];

export const DocsCollection: any = {
  name: "doc",
  label: "Doc<PERSON>",
  path: "docs",
  format: "mdx",
  match: {
    exclude: ["api/**/*"],
  },
  fields: docsFields,
};

export const ZhDocsCollection: any = {
  name: "doc_zh",
  label: "Docs/zh",
  path: "i18n/zh-Hans/docusaurus-plugin-content-docs",
  format: "mdx",
  fields: docs<PERSON>ields,
};
