{"schema": {"version": {"fullVersion": "1.5.16", "major": "1", "minor": "5", "patch": "16"}, "meta": {"flags": ["experimentalData"]}, "collections": [{"name": "doc", "label": "Docs", "path": "docs", "format": "mdx", "match": {"exclude": ["api/**/*"]}, "fields": [{"type": "string", "name": "title", "label": "Title", "isTitle": true, "required": true, "namespace": ["doc", "title"], "searchable": true, "uid": false}, {"type": "string", "name": "description", "label": "Description", "namespace": ["doc", "description"], "searchable": true, "uid": false}, {"label": "Tags", "name": "tags", "type": "string", "list": true, "ui": {"component": "tags"}, "namespace": ["doc", "tags"], "searchable": true, "uid": false}, {"type": "rich-text", "name": "body", "label": "Body", "isBody": true, "templates": [{"name": "Admonition", "ui": {"defaultItem": {"type": "note", "title": "Note"}}, "fields": [{"name": "type", "label": "Type", "type": "string", "options": [{"label": "Note", "value": "note"}, {"label": "Tip", "value": "tip"}, {"label": "Info", "value": "info"}, {"label": "Caution", "value": "caution"}, {"label": "Danger", "value": "danger"}], "namespace": ["doc", "body", "Admonition", "type"], "searchable": true, "uid": false}, {"name": "title", "label": "Title", "type": "string", "isTitle": true, "required": true, "namespace": ["doc", "body", "Admonition", "title"], "searchable": true, "uid": false}, {"name": "children", "label": "Content", "type": "rich-text", "namespace": ["doc", "body", "Admonition", "children"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["doc", "body", "Admonition"]}, {"name": "Details", "fields": [{"name": "summary", "label": "Summary", "type": "string", "isTitle": true, "required": true, "namespace": ["doc", "body", "Details", "summary"], "searchable": true, "uid": false}, {"name": "children", "label": "Details", "type": "rich-text", "namespace": ["doc", "body", "Details", "children"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["doc", "body", "Details"]}, {"name": "CodeBlock", "label": "Code Block", "fields": [{"name": "title", "label": "Filename", "type": "string", "namespace": ["doc", "body", "CodeBlock", "title"], "searchable": true, "uid": false}, {"name": "language", "label": "Language", "type": "string", "namespace": ["doc", "body", "CodeBlock", "language"], "searchable": true, "uid": false}, {"name": "children", "label": "Code", "type": "rich-text", "required": true, "namespace": ["doc", "body", "CodeBlock", "children"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["doc", "body", "CodeBlock"]}, {"name": "Tabs", "fields": [{"name": "children", "label": "Tabs", "type": "rich-text", "templates": [{"name": "TabItem", "label": "Tab", "ui": {"defaultItem": {"label": "Tab", "value": "tab"}}, "fields": [{"name": "label", "label": "Label", "type": "string", "isTitle": true, "required": true, "namespace": ["doc", "body", "Tabs", "children", "TabItem", "label"]}, {"name": "value", "type": "string", "ui": {}, "namespace": ["doc", "body", "Tabs", "children", "TabItem", "value"]}, {"name": "children", "label": "Content", "type": "string", "ui": {"component": "textarea"}, "namespace": ["doc", "body", "Tabs", "children", "TabItem", "children"]}], "namespace": ["doc", "body", "Tabs", "children", "TabItem"]}], "namespace": ["doc", "body", "Tabs", "children"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["doc", "body", "Tabs"]}, {"name": "DocCardList", "label": "Doc Card List", "fields": [{"name": "title", "label": "Title", "type": "string", "namespace": ["doc", "body", "DocCardList", "title"], "searchable": true, "uid": false}], "namespace": ["doc", "body", "DocCardList"]}], "namespace": ["doc", "body"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["doc"]}, {"name": "doc_zh", "label": "Docs/zh", "path": "i18n/zh-<PERSON>/docusaurus-plugin-content-docs", "format": "mdx", "fields": [{"type": "string", "name": "title", "label": "Title", "isTitle": true, "required": true, "namespace": ["doc_zh", "title"], "searchable": true, "uid": false}, {"type": "string", "name": "description", "label": "Description", "namespace": ["doc_zh", "description"], "searchable": true, "uid": false}, {"label": "Tags", "name": "tags", "type": "string", "list": true, "ui": {"component": "tags"}, "namespace": ["doc_zh", "tags"], "searchable": true, "uid": false}, {"type": "rich-text", "name": "body", "label": "Body", "isBody": true, "templates": [{"name": "Admonition", "ui": {"defaultItem": {"type": "note", "title": "Note"}}, "fields": [{"name": "type", "label": "Type", "type": "string", "options": [{"label": "Note", "value": "note"}, {"label": "Tip", "value": "tip"}, {"label": "Info", "value": "info"}, {"label": "Caution", "value": "caution"}, {"label": "Danger", "value": "danger"}], "namespace": ["doc_zh", "body", "Admonition", "type"], "searchable": true, "uid": false}, {"name": "title", "label": "Title", "type": "string", "isTitle": true, "required": true, "namespace": ["doc_zh", "body", "Admonition", "title"], "searchable": true, "uid": false}, {"name": "children", "label": "Content", "type": "rich-text", "namespace": ["doc_zh", "body", "Admonition", "children"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["doc_zh", "body", "Admonition"]}, {"name": "Details", "fields": [{"name": "summary", "label": "Summary", "type": "string", "isTitle": true, "required": true, "namespace": ["doc_zh", "body", "Details", "summary"], "searchable": true, "uid": false}, {"name": "children", "label": "Details", "type": "rich-text", "namespace": ["doc_zh", "body", "Details", "children"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["doc_zh", "body", "Details"]}, {"name": "CodeBlock", "label": "Code Block", "fields": [{"name": "title", "label": "Filename", "type": "string", "namespace": ["doc_zh", "body", "CodeBlock", "title"], "searchable": true, "uid": false}, {"name": "language", "label": "Language", "type": "string", "namespace": ["doc_zh", "body", "CodeBlock", "language"], "searchable": true, "uid": false}, {"name": "children", "label": "Code", "type": "rich-text", "required": true, "namespace": ["doc_zh", "body", "CodeBlock", "children"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["doc_zh", "body", "CodeBlock"]}, {"name": "Tabs", "fields": [{"name": "children", "label": "Tabs", "type": "rich-text", "templates": [{"name": "TabItem", "label": "Tab", "ui": {"defaultItem": {"label": "Tab", "value": "tab"}}, "fields": [{"name": "label", "label": "Label", "type": "string", "isTitle": true, "required": true, "namespace": ["doc_zh", "body", "Tabs", "children", "TabItem", "label"]}, {"name": "value", "type": "string", "ui": {}, "namespace": ["doc_zh", "body", "Tabs", "children", "TabItem", "value"]}, {"name": "children", "label": "Content", "type": "string", "ui": {"component": "textarea"}, "namespace": ["doc_zh", "body", "Tabs", "children", "TabItem", "children"]}], "namespace": ["doc_zh", "body", "Tabs", "children", "TabItem"]}], "namespace": ["doc_zh", "body", "Tabs", "children"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["doc_zh", "body", "Tabs"]}, {"name": "DocCardList", "label": "Doc Card List", "fields": [{"name": "title", "label": "Title", "type": "string", "namespace": ["doc_zh", "body", "DocCardList", "title"], "searchable": true, "uid": false}], "namespace": ["doc_zh", "body", "DocCardList"]}], "namespace": ["doc_zh", "body"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["doc_zh"]}, {"name": "homepage", "label": "Homepage", "description": "To see settings changes reflected on your site, you must restart the Tina CLI after saving changes (local development only).", "path": "config/homepage", "format": "json", "ui": {"allowedActions": {"create": false, "delete": false}}, "fields": [{"type": "string", "name": "_warning", "ui": {}, "namespace": ["homepage", "_warning"], "searchable": true, "uid": false}, {"type": "string", "label": "Label", "name": "label", "required": true, "isTitle": true, "ui": {"component": "hidden"}, "namespace": ["homepage", "label"], "searchable": true, "uid": false}, {"type": "string", "name": "title", "label": "Title", "namespace": ["homepage", "title"], "searchable": true, "uid": false}, {"type": "string", "name": "description", "label": "Description", "namespace": ["homepage", "description"], "searchable": true, "uid": false}, {"type": "object", "list": true, "name": "blocks", "label": "Blocks", "templates": [{"name": "hero", "label": "Hero", "fields": [{"name": "title", "label": "Title", "description": "By default this is the site title", "type": "string", "namespace": ["homepage", "blocks", "hero", "title"], "searchable": true, "uid": false}, {"name": "subtitle", "label": "Subtitle", "description": "By default this is the site tagline", "type": "string", "namespace": ["homepage", "blocks", "hero", "subtitle"], "searchable": true, "uid": false}, {"label": "Document Link", "name": "document", "type": "reference", "collections": ["doc"], "namespace": ["homepage", "blocks", "hero", "document"], "searchable": true, "uid": false}, {"name": "documentLabel", "label": "Button Text", "type": "string", "namespace": ["homepage", "blocks", "hero", "documentLabel"], "searchable": true, "uid": false}], "namespace": ["homepage", "blocks", "hero"]}, {"name": "features", "label": "Features", "fields": [{"name": "items", "label": "Features", "type": "object", "list": true, "ui": {}, "fields": [{"name": "title", "label": "Title", "type": "string", "namespace": ["homepage", "blocks", "features", "items", "title"]}, {"name": "description", "label": "Description", "type": "rich-text", "namespace": ["homepage", "blocks", "features", "items", "description"]}, {"name": "image", "label": "Image", "type": "image", "namespace": ["homepage", "blocks", "features", "items", "image"]}], "namespace": ["homepage", "blocks", "features", "items"], "searchable": true, "uid": false}], "namespace": ["homepage", "blocks", "features"]}, {"name": "youTubeEmbed", "label": "YouTube Embed", "fields": [{"name": "title", "label": "Title", "type": "string", "namespace": ["homepage", "blocks", "youTubeEmbed", "title"], "searchable": true, "uid": false}, {"name": "url", "label": "YouTube URL", "type": "string", "namespace": ["homepage", "blocks", "youTubeEmbed", "url"], "searchable": true, "uid": false}, {"name": "caption", "label": "Caption", "type": "string", "namespace": ["homepage", "blocks", "youTubeEmbed", "caption"], "searchable": true, "uid": false}], "namespace": ["homepage", "blocks", "youTubeEmbed"]}], "namespace": ["homepage", "blocks"], "searchable": true, "uid": false}], "namespace": ["homepage"]}, {"name": "pages", "label": "Pages", "path": "src/pages", "format": "mdx", "fields": [{"type": "string", "name": "title", "label": "Title", "isTitle": true, "required": true, "namespace": ["pages", "title"], "searchable": true, "uid": false}, {"type": "string", "name": "description", "label": "Description", "namespace": ["pages", "description"], "searchable": true, "uid": false}, {"type": "rich-text", "name": "body", "label": "Body", "isBody": true, "templates": [{"name": "Admonition", "ui": {"defaultItem": {"type": "note", "title": "Note"}}, "fields": [{"name": "type", "label": "Type", "type": "string", "options": [{"label": "Note", "value": "note"}, {"label": "Tip", "value": "tip"}, {"label": "Info", "value": "info"}, {"label": "Caution", "value": "caution"}, {"label": "Danger", "value": "danger"}], "namespace": ["pages", "body", "Admonition", "type"], "searchable": true, "uid": false}, {"name": "title", "label": "Title", "type": "string", "isTitle": true, "required": true, "namespace": ["pages", "body", "Admonition", "title"], "searchable": true, "uid": false}, {"name": "children", "label": "Content", "type": "rich-text", "namespace": ["pages", "body", "Admonition", "children"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["pages", "body", "Admonition"]}, {"name": "Details", "fields": [{"name": "summary", "label": "Summary", "type": "string", "isTitle": true, "required": true, "namespace": ["pages", "body", "Details", "summary"], "searchable": true, "uid": false}, {"name": "children", "label": "Details", "type": "rich-text", "namespace": ["pages", "body", "Details", "children"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["pages", "body", "Details"]}, {"name": "CodeBlock", "label": "Code Block", "fields": [{"name": "title", "label": "Filename", "type": "string", "namespace": ["pages", "body", "CodeBlock", "title"], "searchable": true, "uid": false}, {"name": "language", "label": "Language", "type": "string", "namespace": ["pages", "body", "CodeBlock", "language"], "searchable": true, "uid": false}, {"name": "children", "label": "Code", "type": "rich-text", "required": true, "namespace": ["pages", "body", "CodeBlock", "children"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["pages", "body", "CodeBlock"]}, {"name": "Tabs", "fields": [{"name": "children", "label": "Tabs", "type": "rich-text", "templates": [{"name": "TabItem", "label": "Tab", "ui": {"defaultItem": {"label": "Tab", "value": "tab"}}, "fields": [{"name": "label", "label": "Label", "type": "string", "isTitle": true, "required": true, "namespace": ["pages", "body", "Tabs", "children", "TabItem", "label"]}, {"name": "value", "type": "string", "ui": {}, "namespace": ["pages", "body", "Tabs", "children", "TabItem", "value"]}, {"name": "children", "label": "Content", "type": "string", "ui": {"component": "textarea"}, "namespace": ["pages", "body", "Tabs", "children", "TabItem", "children"]}], "namespace": ["pages", "body", "Tabs", "children", "TabItem"]}], "namespace": ["pages", "body", "Tabs", "children"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["pages", "body", "Tabs"]}, {"name": "DocCardList", "label": "Doc Card List", "fields": [{"name": "title", "label": "Title", "type": "string", "namespace": ["pages", "body", "DocCardList", "title"], "searchable": true, "uid": false}], "namespace": ["pages", "body", "DocCardList"]}], "namespace": ["pages", "body"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["pages"]}, {"name": "sidebar", "label": "Docs Sidebar", "path": "config/sidebar", "format": "json", "ui": {"global": true, "allowedActions": {"create": false, "delete": false}}, "fields": [{"type": "string", "name": "_warning", "ui": {}, "namespace": ["sidebar", "_warning"], "searchable": true, "uid": false}, {"name": "items", "label": "Items", "type": "object", "list": true, "ui": {}, "fields": [{"type": "string", "label": "Label", "name": "label", "required": true, "isTitle": true, "namespace": ["sidebar", "items", "label"], "searchable": true, "uid": false}, {"name": "items", "label": "Items", "type": "object", "list": true, "templates": [{"name": "category", "label": "Category", "ui": {"defaultItem": {"link": "none"}}, "fields": [{"name": "title", "label": "Title", "type": "string", "isTitle": true, "required": true, "namespace": ["sidebar", "items", "items", "category", "title"], "searchable": true, "uid": false}, {"name": "link", "label": "Link", "type": "string", "options": [{"label": "None", "value": "none"}, {"label": "Document", "value": "doc"}, {"label": "Generated Index", "value": "generated"}], "namespace": ["sidebar", "items", "items", "category", "link"], "searchable": true, "uid": false}, {"name": "docLink", "label": "Document", "type": "reference", "collections": ["doc"], "ui": {}, "namespace": ["sidebar", "items", "items", "category", "docLink"], "searchable": true, "uid": false}, {"name": "items", "label": "Items", "type": "object", "list": true, "templates": [{"name": "category", "label": "Category", "ui": {"defaultItem": {"link": "none"}}, "fields": [{"name": "title", "label": "Title", "type": "string", "isTitle": true, "required": true, "namespace": ["sidebar", "items", "items", "category", "items", "category", "title"]}, {"name": "link", "label": "Link", "type": "string", "options": [{"label": "None", "value": "none"}, {"label": "Document", "value": "doc"}, {"label": "Generated Index", "value": "generated"}], "namespace": ["sidebar", "items", "items", "category", "items", "category", "link"]}, {"name": "docLink", "label": "Document", "type": "reference", "collections": ["doc"], "ui": {}, "namespace": ["sidebar", "items", "items", "category", "items", "category", "docLink"]}, {"name": "items", "label": "Items", "type": "object", "list": true, "templates": [{"name": "category", "label": "Category", "ui": {"defaultItem": {"link": "none"}}, "fields": [{"name": "title", "label": "Title", "type": "string", "isTitle": true, "required": true, "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "category", "title"]}, {"name": "link", "label": "Link", "type": "string", "options": [{"label": "None", "value": "none"}, {"label": "Document", "value": "doc"}, {"label": "Generated Index", "value": "generated"}], "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "category", "link"]}, {"name": "docLink", "label": "Document", "type": "reference", "collections": ["doc"], "ui": {}, "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "category", "docLink"]}, {"name": "items", "label": "Items", "type": "object", "list": true, "templates": [{"name": "doc", "label": "<PERSON>", "ui": {}, "fields": [{"label": "Document", "name": "document", "type": "reference", "collections": ["doc"], "isTitle": true, "required": true, "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "category", "items", "doc", "document"]}, {"name": "label", "label": "Label", "description": "By default this is the document title", "type": "string", "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "category", "items", "doc", "label"]}], "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "category", "items", "doc"]}, {"name": "link", "label": "External Link", "ui": {}, "fields": [{"name": "title", "label": "Label", "type": "string", "isTitle": true, "required": true, "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "category", "items", "link", "title"]}, {"name": "href", "label": "URL", "type": "string", "required": true, "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "category", "items", "link", "href"]}], "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "category", "items", "link"]}], "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "category", "items"]}], "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "category"]}, {"name": "doc", "label": "<PERSON>", "ui": {}, "fields": [{"label": "Document", "name": "document", "type": "reference", "collections": ["doc"], "isTitle": true, "required": true, "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "doc", "document"]}, {"name": "label", "label": "Label", "description": "By default this is the document title", "type": "string", "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "doc", "label"]}], "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "doc"]}, {"name": "link", "label": "External Link", "ui": {}, "fields": [{"name": "title", "label": "Label", "type": "string", "isTitle": true, "required": true, "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "link", "title"]}, {"name": "href", "label": "URL", "type": "string", "required": true, "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "link", "href"]}], "namespace": ["sidebar", "items", "items", "category", "items", "category", "items", "link"]}], "namespace": ["sidebar", "items", "items", "category", "items", "category", "items"]}], "namespace": ["sidebar", "items", "items", "category", "items", "category"]}, {"name": "doc", "label": "<PERSON>", "ui": {}, "fields": [{"label": "Document", "name": "document", "type": "reference", "collections": ["doc"], "isTitle": true, "required": true, "namespace": ["sidebar", "items", "items", "category", "items", "doc", "document"]}, {"name": "label", "label": "Label", "description": "By default this is the document title", "type": "string", "namespace": ["sidebar", "items", "items", "category", "items", "doc", "label"]}], "namespace": ["sidebar", "items", "items", "category", "items", "doc"]}, {"name": "link", "label": "External Link", "ui": {}, "fields": [{"name": "title", "label": "Label", "type": "string", "isTitle": true, "required": true, "namespace": ["sidebar", "items", "items", "category", "items", "link", "title"]}, {"name": "href", "label": "URL", "type": "string", "required": true, "namespace": ["sidebar", "items", "items", "category", "items", "link", "href"]}], "namespace": ["sidebar", "items", "items", "category", "items", "link"]}], "namespace": ["sidebar", "items", "items", "category", "items"], "searchable": true, "uid": false}], "namespace": ["sidebar", "items", "items", "category"]}, {"name": "doc", "label": "<PERSON>", "ui": {}, "fields": [{"label": "Document", "name": "document", "type": "reference", "collections": ["doc"], "isTitle": true, "required": true, "namespace": ["sidebar", "items", "items", "doc", "document"], "searchable": true, "uid": false}, {"name": "label", "label": "Label", "description": "By default this is the document title", "type": "string", "namespace": ["sidebar", "items", "items", "doc", "label"], "searchable": true, "uid": false}], "namespace": ["sidebar", "items", "items", "doc"]}, {"name": "link", "label": "External Link", "ui": {}, "fields": [{"name": "title", "label": "Label", "type": "string", "isTitle": true, "required": true, "namespace": ["sidebar", "items", "items", "link", "title"], "searchable": true, "uid": false}, {"name": "href", "label": "URL", "type": "string", "required": true, "namespace": ["sidebar", "items", "items", "link", "href"], "searchable": true, "uid": false}], "namespace": ["sidebar", "items", "items", "link"]}], "namespace": ["sidebar", "items", "items"], "searchable": true, "uid": false}], "namespace": ["sidebar", "items"], "searchable": true, "uid": false}], "namespace": ["sidebar"]}, {"label": "Settings", "name": "settings", "path": "config/docusaurus", "format": "json", "ui": {"global": true, "allowedActions": {"create": false, "delete": false}}, "fields": [{"type": "string", "name": "_warning", "ui": {}, "namespace": ["settings", "_warning"], "searchable": true, "uid": false}, {"type": "string", "label": "Label", "name": "label", "required": true, "isTitle": true, "ui": {"component": "hidden"}, "namespace": ["settings", "label"], "searchable": true, "uid": false}, {"type": "object", "label": "Logo", "name": "logo", "fields": [{"type": "string", "label": "Alt Text", "name": "alt", "namespace": ["settings", "logo", "alt"], "searchable": true, "uid": false}, {"type": "image", "label": "Source", "name": "src", "namespace": ["settings", "logo", "src"], "searchable": false, "uid": false}], "namespace": ["settings", "logo"], "searchable": true, "uid": false}, {"type": "string", "label": "Title", "name": "title", "required": true, "namespace": ["settings", "title"], "searchable": true, "uid": false}, {"type": "string", "label": "Tagline", "name": "tagline", "namespace": ["settings", "tagline"], "searchable": true, "uid": false}, {"type": "object", "label": "<PERSON><PERSON><PERSON>", "name": "navbar", "list": true, "ui": {"defaultItem": {"position": "left"}}, "fields": [{"name": "label", "label": "Label", "type": "string", "isTitle": true, "required": true, "namespace": ["settings", "navbar", "label"], "searchable": true, "uid": false}, {"name": "link", "label": "Link", "type": "string", "options": [{"label": "None", "value": "none"}, {"label": "Document", "value": "doc"}, {"label": "Page", "value": "page"}, {"label": "External", "value": "external"}], "namespace": ["settings", "navbar", "link"], "searchable": true, "uid": false}, {"name": "docLink", "label": "Document", "type": "reference", "collections": ["doc"], "ui": {}, "namespace": ["settings", "navbar", "docLink"], "searchable": true, "uid": false}, {"name": "pageLink", "label": "Page", "type": "reference", "collections": ["pages"], "ui": {}, "namespace": ["settings", "navbar", "pageLink"], "searchable": true, "uid": false}, {"name": "externalLink", "label": "URL", "type": "string", "ui": {}, "namespace": ["settings", "navbar", "externalLink"], "searchable": true, "uid": false}, {"name": "position", "label": "Position", "type": "string", "required": true, "options": [{"label": "Left", "value": "left"}, {"label": "Right", "value": "right"}], "ui": {"component": "button-toggle"}, "namespace": ["settings", "navbar", "position"], "searchable": true, "uid": false}, {"name": "items", "label": "Items", "type": "object", "list": true, "ui": {}, "fields": [{"name": "label", "label": "Label", "type": "string", "isTitle": true, "required": true, "namespace": ["settings", "navbar", "items", "label"], "searchable": true, "uid": false}, {"name": "link", "label": "Link", "type": "string", "options": [{"label": "None", "value": "none"}, {"label": "Document", "value": "doc"}, {"label": "Page", "value": "page"}, {"label": "External", "value": "external"}], "namespace": ["settings", "navbar", "items", "link"], "searchable": true, "uid": false}, {"name": "docLink", "label": "Document", "type": "reference", "collections": ["doc"], "ui": {}, "namespace": ["settings", "navbar", "items", "docLink"], "searchable": true, "uid": false}, {"name": "pageLink", "label": "Page", "type": "reference", "collections": ["pages"], "ui": {}, "namespace": ["settings", "navbar", "items", "pageLink"], "searchable": true, "uid": false}, {"name": "externalLink", "label": "URL", "type": "string", "ui": {}, "namespace": ["settings", "navbar", "items", "externalLink"], "searchable": true, "uid": false}, {"name": "position", "label": "Position", "type": "string", "required": true, "options": [{"label": "Left", "value": "left"}, {"label": "Right", "value": "right"}], "ui": {"component": "button-toggle"}, "namespace": ["settings", "navbar", "items", "position"], "searchable": true, "uid": false}, {"name": "items", "label": "Items", "type": "object", "list": true, "ui": {}, "fields": [{"name": "label", "label": "Label", "type": "string", "isTitle": true, "required": true, "namespace": ["settings", "navbar", "items", "items", "label"], "searchable": true, "uid": false}, {"name": "link", "label": "Link", "type": "string", "options": [{"label": "None", "value": "none"}, {"label": "Document", "value": "doc"}, {"label": "Page", "value": "page"}, {"label": "External", "value": "external"}], "namespace": ["settings", "navbar", "items", "items", "link"], "searchable": true, "uid": false}, {"name": "docLink", "label": "Document", "type": "reference", "collections": ["doc"], "ui": {}, "namespace": ["settings", "navbar", "items", "items", "docLink"], "searchable": true, "uid": false}, {"name": "pageLink", "label": "Page", "type": "reference", "collections": ["pages"], "ui": {}, "namespace": ["settings", "navbar", "items", "items", "pageLink"], "searchable": true, "uid": false}, {"name": "externalLink", "label": "URL", "type": "string", "ui": {}, "namespace": ["settings", "navbar", "items", "items", "externalLink"], "searchable": true, "uid": false}, {"name": "position", "label": "Position", "type": "string", "required": true, "options": [{"label": "Left", "value": "left"}, {"label": "Right", "value": "right"}], "ui": {"component": "button-toggle"}, "namespace": ["settings", "navbar", "items", "items", "position"], "searchable": true, "uid": false}], "namespace": ["settings", "navbar", "items", "items"], "searchable": true, "uid": false}], "namespace": ["settings", "navbar", "items"], "searchable": true, "uid": false}], "namespace": ["settings", "navbar"], "searchable": true, "uid": false}, {"type": "object", "label": "Footer", "name": "footer", "fields": [{"name": "style", "label": "Style", "type": "string", "options": [{"label": "Dark", "value": "dark"}, {"label": "Light", "value": "light"}], "ui": {"component": "button-toggle"}, "namespace": ["settings", "footer", "style"], "searchable": true, "uid": false}, {"type": "object", "label": "Categories", "name": "links", "list": true, "ui": {}, "fields": [{"type": "string", "label": "Title", "name": "title", "namespace": ["settings", "footer", "links", "title"], "searchable": true, "uid": false}, {"type": "object", "label": "Links", "name": "items", "list": true, "templates": [{"name": "internal_doc", "label": "Internal Doc", "ui": {}, "fields": [{"type": "string", "label": "Label", "name": "label", "namespace": ["settings", "footer", "links", "items", "internal_doc", "label"], "searchable": true, "uid": false}, {"type": "reference", "label": "<PERSON>", "name": "docLink", "collections": ["doc"], "namespace": ["settings", "footer", "links", "items", "internal_doc", "docLink"], "searchable": true, "uid": false}], "namespace": ["settings", "footer", "links", "items", "internal_doc"]}, {"name": "internal_page", "label": "Internal Page", "ui": {}, "fields": [{"type": "string", "label": "Label", "name": "label", "namespace": ["settings", "footer", "links", "items", "internal_page", "label"], "searchable": true, "uid": false}, {"type": "reference", "label": "Page Link", "name": "pageLink", "collections": ["pages"], "namespace": ["settings", "footer", "links", "items", "internal_page", "pageLink"], "searchable": true, "uid": false}], "namespace": ["settings", "footer", "links", "items", "internal_page"]}, {"name": "blog", "label": "Blog", "ui": {"defaultItem": {"label": "Blog"}}, "fields": [{"type": "string", "label": "Label", "name": "label", "namespace": ["settings", "footer", "links", "items", "blog", "label"], "searchable": true, "uid": false}], "namespace": ["settings", "footer", "links", "items", "blog"]}, {"name": "external", "label": "External", "ui": {}, "fields": [{"type": "string", "label": "Label", "name": "label", "namespace": ["settings", "footer", "links", "items", "external", "label"], "searchable": true, "uid": false}, {"type": "string", "label": "URL", "name": "href", "namespace": ["settings", "footer", "links", "items", "external", "href"], "searchable": true, "uid": false}], "namespace": ["settings", "footer", "links", "items", "external"]}], "namespace": ["settings", "footer", "links", "items"], "searchable": true, "uid": false}], "namespace": ["settings", "footer", "links"], "searchable": true, "uid": false}, {"type": "string", "label": "Copyright", "name": "copyright", "namespace": ["settings", "footer", "copyright"], "searchable": true, "uid": false}], "namespace": ["settings", "footer"], "searchable": true, "uid": false}], "namespace": ["settings"]}], "config": {"media": {"tina": {"publicFolder": "static", "mediaRoot": "img"}}}}, "lookup": {"DocumentConnection": {"type": "DocumentConnection", "resolveType": "multiCollectionDocumentList", "collections": ["doc", "doc_zh", "homepage", "pages", "sidebar", "settings"]}, "Node": {"type": "Node", "resolveType": "nodeDocument"}, "DocumentNode": {"type": "DocumentNode", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "Doc": {"type": "Doc", "resolveType": "collectionDocument", "collection": "doc", "createDoc": "create", "updateDoc": "update"}, "DocConnection": {"type": "DocConnection", "resolveType": "collectionDocumentList", "collection": "doc"}, "Doc_zh": {"type": "Doc_zh", "resolveType": "collectionDocument", "collection": "doc_zh", "createDoc_zh": "create", "updateDoc_zh": "update"}, "Doc_zhConnection": {"type": "Doc_zhConnection", "resolveType": "collectionDocumentList", "collection": "doc_zh"}, "HomepageBlocksHeroDocument": {"type": "HomepageBlocksHeroDocument", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "HomepageBlocks": {"type": "HomepageBlocks", "resolveType": "unionData", "typeMap": {"hero": "HomepageBlocksHero", "features": "HomepageBlocksFeatures", "youTubeEmbed": "HomepageBlocksYouTubeEmbed"}}, "Homepage": {"type": "Homepage", "resolveType": "collectionDocument", "collection": "homepage", "createHomepage": "create", "updateHomepage": "update"}, "HomepageConnection": {"type": "HomepageConnection", "resolveType": "collectionDocumentList", "collection": "homepage"}, "Pages": {"type": "Pages", "resolveType": "collectionDocument", "collection": "pages", "createPages": "create", "updatePages": "update"}, "PagesConnection": {"type": "PagesConnection", "resolveType": "collectionDocumentList", "collection": "pages"}, "SidebarItemsItemsCategoryDocLink": {"type": "SidebarItemsItemsCategoryDocLink", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SidebarItemsItemsCategoryItemsCategoryDocLink": {"type": "SidebarItemsItemsCategoryItemsCategoryDocLink", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SidebarItemsItemsCategoryItemsCategoryItemsCategoryDocLink": {"type": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryDocLink", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsDocDocument": {"type": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsDocDocument", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItems": {"type": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItems", "resolveType": "unionData", "typeMap": {"doc": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsDoc", "link": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsLink"}}, "SidebarItemsItemsCategoryItemsCategoryItemsDocDocument": {"type": "SidebarItemsItemsCategoryItemsCategoryItemsDocDocument", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SidebarItemsItemsCategoryItemsCategoryItems": {"type": "SidebarItemsItemsCategoryItemsCategoryItems", "resolveType": "unionData", "typeMap": {"category": "SidebarItemsItemsCategoryItemsCategoryItemsCategory", "doc": "SidebarItemsItemsCategoryItemsCategoryItemsDoc", "link": "SidebarItemsItemsCategoryItemsCategoryItemsLink"}}, "SidebarItemsItemsCategoryItemsDocDocument": {"type": "SidebarItemsItemsCategoryItemsDocDocument", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SidebarItemsItemsCategoryItems": {"type": "SidebarItemsItemsCategoryItems", "resolveType": "unionData", "typeMap": {"category": "SidebarItemsItemsCategoryItemsCategory", "doc": "SidebarItemsItemsCategoryItemsDoc", "link": "SidebarItemsItemsCategoryItemsLink"}}, "SidebarItemsItemsDocDocument": {"type": "SidebarItemsItemsDocDocument", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SidebarItemsItems": {"type": "SidebarItemsItems", "resolveType": "unionData", "typeMap": {"category": "SidebarItemsItemsCategory", "doc": "SidebarItemsItemsDoc", "link": "SidebarItemsItemsLink"}}, "Sidebar": {"type": "Sidebar", "resolveType": "collectionDocument", "collection": "sidebar", "createSidebar": "create", "updateSidebar": "update"}, "SidebarConnection": {"type": "SidebarConnection", "resolveType": "collectionDocumentList", "collection": "sidebar"}, "SettingsNavbarDocLink": {"type": "SettingsNavbarDocLink", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SettingsNavbarPageLink": {"type": "SettingsNavbarPageLink", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SettingsNavbarItemsDocLink": {"type": "SettingsNavbarItemsDocLink", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SettingsNavbarItemsPageLink": {"type": "SettingsNavbarItemsPageLink", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SettingsNavbarItemsItemsDocLink": {"type": "SettingsNavbarItemsItemsDocLink", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SettingsNavbarItemsItemsPageLink": {"type": "SettingsNavbarItemsItemsPageLink", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SettingsFooterLinksItemsInternal_docDocLink": {"type": "SettingsFooterLinksItemsInternal_docDocLink", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SettingsFooterLinksItemsInternal_pagePageLink": {"type": "SettingsFooterLinksItemsInternal_pagePageLink", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "SettingsFooterLinksItems": {"type": "SettingsFooterLinksItems", "resolveType": "unionData", "typeMap": {"internal_doc": "SettingsFooterLinksItemsInternal_doc", "internal_page": "SettingsFooterLinksItemsInternal_page", "blog": "SettingsFooterLinksItemsBlog", "external": "SettingsFooterLinksItemsExternal"}}, "Settings": {"type": "Settings", "resolveType": "collectionDocument", "collection": "settings", "createSettings": "create", "updateSettings": "update"}, "SettingsConnection": {"type": "SettingsConnection", "resolveType": "collectionDocumentList", "collection": "settings"}}, "graphql": {"kind": "Document", "definitions": [{"kind": "ScalarTypeDefinition", "name": {"kind": "Name", "value": "Reference"}, "description": {"kind": "StringValue", "value": "References another document, used as a foreign key"}, "directives": []}, {"kind": "ScalarTypeDefinition", "name": {"kind": "Name", "value": "JSON"}, "description": {"kind": "StringValue", "value": ""}, "directives": []}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SystemInfo"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "filename"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "basename"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasReferences"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "breadcrumbs"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "excludeExtension"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}], "type": {"kind": "NonNullType", "type": {"kind": "ListType", "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "relativePath"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "extension"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "template"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collection"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Folder"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "name"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageInfo"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasPreviousPage"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasNextPage"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "startCursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "endCursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": ""}, "name": {"kind": "Name", "value": "Node"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": ""}, "name": {"kind": "Name", "value": "Document"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": "A relay-compliant pagination connection"}, "name": {"kind": "Name", "value": "Connection"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Query"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "getOptimizedQuery"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "queryString"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collections"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "ListType", "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "id"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "document"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "doc"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "docConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocConnection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "doc_zh"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zh"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "doc_zhConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zh<PERSON><PERSON>er"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhConnection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "homepage"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Homepage"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "homepageConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageFilter"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageConnection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pages"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Pages"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pagesConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesFilter"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesConnection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "sidebar"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Sidebar"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "sidebarConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarFilter"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarConnection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "settings"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Settings"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "settingsConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsConnection"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc_zh"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zh<PERSON><PERSON>er"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "homepage"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pages"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sidebar"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "settings"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "DocumentConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "DocumentConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Collection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "name"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "slug"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "format"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "matches"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "templates"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "fields"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "documents"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "folder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentConnection"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "DocumentNode"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zh"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Homepage"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Pages"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Sidebar"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Settings"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Folder"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Doc"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "tags"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "body"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "StringFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "startsWith"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "in"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "RichTextFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "startsWith"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocBodyAdmonitionFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "type"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocBodyDetailsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "summary"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocBodyCodeBlockFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "language"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocBodyTabsChildrenTabItemFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "value"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocBodyTabsChil<PERSON>n<PERSON>ilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "TabItem"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocBodyTabsChildrenTabItemFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocBodyTabsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocBodyTabsChil<PERSON>n<PERSON>ilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocBodyDocCardListFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocBodyFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "Admonition"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocBodyAdmonitionFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "Details"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocBodyDetailsFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "CodeBlock"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocBodyCodeBlockFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "Tabs"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocBodyTabsFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "DocCardList"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocBodyDocCardListFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocBodyFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "DocConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "DocConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Doc_zh"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "tags"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "body"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "Doc_zhBodyAdmonitionFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "type"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "Doc_zhBodyDetailsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "summary"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "Doc_zhBodyCodeBlockFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "language"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "Doc_zhBodyTabsChildrenTabItemFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "value"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "Doc_zhBodyTabsChildrenFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "TabItem"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhBodyTabsChildrenTabItemFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "Doc_zhBodyTabsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhBodyTabsChildrenFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "Doc_zhBodyDocCardListFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "Doc_zhBodyFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "Admonition"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhBodyAdmonitionFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "Details"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhBodyDetailsFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "CodeBlock"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhBodyCodeBlockFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "Tabs"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhBodyTabsFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "DocCardList"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhBodyDocCardListFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "Doc_zh<PERSON><PERSON>er"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhBodyFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Doc_zhConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zh"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "Doc_zhConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhConnectionEdges"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "HomepageBlocksHeroDocument"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "HomepageBlocksHero"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "subtitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "document"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksHeroDocument"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "documentLabel"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "HomepageBlocksFeaturesItems"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "image"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "HomepageBlocksFeatures"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "items"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksFeaturesItems"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "HomepageBlocksYouTubeEmbed"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "url"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "caption"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "HomepageBlocks"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksHero"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksFeatures"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksYouTubeEmbed"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Homepage"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_warning"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "blocks"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocks"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "HomepageBlocksHeroDocumentFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "HomepageBlocksHeroFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "subtitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "document"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksHeroDocumentFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "documentLabel"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "ImageFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "startsWith"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "in"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "HomepageBlocksFeaturesItemsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "image"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "HomepageBlocksFeaturesFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksFeaturesItemsFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "HomepageBlocksYouTubeEmbedFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "url"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "caption"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "HomepageBlocksFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "hero"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksHeroFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "features"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksFeaturesFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "youTubeEmbed"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksYouTubeEmbedFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "HomepageFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "_warning"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blocks"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "HomepageConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Homepage"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "HomepageConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Pages"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "body"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PagesBodyAdmonitionFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "type"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PagesBodyDetailsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "summary"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PagesBodyCodeBlockFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "language"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PagesBodyTabsChildrenTabItemFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "value"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PagesBodyTabs<PERSON><PERSON><PERSON>n<PERSON><PERSON>er"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "TabItem"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesBodyTabsChildrenTabItemFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PagesBodyTabsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesBodyTabs<PERSON><PERSON><PERSON>n<PERSON><PERSON>er"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PagesBodyDocCardListFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PagesBodyFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "Admonition"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesBodyAdmonitionFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "Details"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesBodyDetailsFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "CodeBlock"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesBodyCodeBlockFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "Tabs"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesBodyTabsFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "DocCardList"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesBodyDocCardListFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PagesFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesBodyFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PagesConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Pages"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "PagesConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesConnectionEdges"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryDocLink"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryDocLink"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryDocLink"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsDocDocument"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsDoc"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "document"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsDocDocument"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsLink"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "href"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItems"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsDoc"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsLink"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategory"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "link"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "docLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryDocLink"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "items"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItems"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsDocDocument"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsDoc"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "document"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsDocDocument"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsLink"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "href"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItems"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategory"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsDoc"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsLink"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategory"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "link"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "docLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryDocLink"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "items"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItems"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsDocDocument"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsDoc"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "document"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsDocDocument"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsLink"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "href"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItems"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategory"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsDoc"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsLink"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SidebarItemsItemsCategory"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "link"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "docLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryDocLink"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "items"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItems"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsDocDocument"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SidebarItemsItemsDoc"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "document"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsDocDocument"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SidebarItemsItemsLink"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "href"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItems"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategory"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsDoc"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsLink"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SidebarItems"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "items"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItems"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Sidebar"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_warning"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "items"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItems"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryDocLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryDocLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryDocLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsDocDocumentFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsDocFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "document"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsDocDocumentFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "href"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsDocFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsLinkFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryDocLinkFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsDocDocumentFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsDocFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "document"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsDocDocumentFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "href"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "category"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsDocFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsLinkFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryDocLinkFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsDocDocumentFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsDocFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "document"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsDocDocumentFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "href"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "category"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsDocFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsLinkFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryDocLinkFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsDocDocumentFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsDocFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "document"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsDocDocumentFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "href"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "category"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsDocFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsLinkFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "_warning"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SidebarConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Sidebar"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "SidebarConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SettingsLogo"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "alt"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "src"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarDocLink"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarPageLink"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Pages"}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarItemsDocLink"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarItemsPageLink"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Pages"}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarItemsItemsDocLink"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarItemsItemsPageLink"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Pages"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SettingsNavbarItemsItems"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "link"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "docLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItemsItemsDocLink"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItemsItemsPageLink"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "externalLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "position"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SettingsNavbarItems"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "link"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "docLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItemsDocLink"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItemsPageLink"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "externalLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "position"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "items"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItemsItems"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SettingsNavbar"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "link"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "docLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarDocLink"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarPageLink"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "externalLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "position"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "items"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItems"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_docDocLink"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_doc"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "docLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_docDocLink"}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_pagePageLink"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Pages"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_page"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_pagePageLink"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SettingsFooterLinksItemsBlog"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SettingsFooterLinksItemsExternal"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "href"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItems"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_doc"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_page"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsBlog"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsExternal"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SettingsFooterLinks"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "items"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItems"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "style"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "links"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinks"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "copyright"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Settings"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_warning"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "logo"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsLogo"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "tagline"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "navbar"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbar"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "footer"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsLogoFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "alt"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "src"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarDocLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarPageLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pages"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarItemsDocLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarItemsPageLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pages"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarItemsItemsDocLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarItemsItemsPageLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pages"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarItemsItemsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItemsItemsDocLinkFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pageLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItemsItemsPageLinkFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "externalLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "position"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarItemsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItemsDocLinkFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pageLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItemsPageLinkFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "externalLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "position"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItemsItemsFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarDocLinkFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pageLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarPageLinkFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "externalLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "position"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItemsFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_docDocLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_docFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_docDocLinkFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_pagePageLinkFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pages"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_pageFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pageLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_pagePageLinkFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsBlogFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsExternalFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "href"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "internal_doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_docFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "internal_page"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_pageFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blog"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsBlogFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "external"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsExternalFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "style"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "links"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "copyright"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "_warning"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "logo"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsLogoFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tagline"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "navbar"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "footer"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SettingsConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Settings"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "SettingsConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Mutation"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "addPendingDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "template"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentUpdateMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "deleteDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createFolder"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateDoc"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createDoc"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateDoc_zh"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zh"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createDoc_zh"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zh"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateHomepage"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Homepage"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createHomepage"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Homepage"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updatePages"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Pages"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createPages"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Pages"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateSidebar"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Sidebar"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createSidebar"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Sidebar"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateSettings"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Settings"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createSettings"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Settings"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentUpdateMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc_zh"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "homepage"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pages"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sidebar"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "settings"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc_zh"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Doc_zhMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "homepage"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pages"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PagesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sidebar"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "settings"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "Doc_zhMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "HomepageBlocksHeroMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "subtitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "document"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "documentLabel"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "HomepageBlocksFeaturesItemsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "image"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "HomepageBlocksFeaturesMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksFeaturesItemsMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "HomepageBlocksYouTubeEmbedMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "url"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "caption"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "HomepageBlocksMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "hero"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksHeroMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "features"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksFeaturesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "youTubeEmbed"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksYouTubeEmbedMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "HomepageMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "_warning"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blocks"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "HomepageBlocksMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PagesMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsDocMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "document"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsLinkMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "href"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsDocMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsLinkMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryItemsMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsDocMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "document"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsLinkMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "href"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "category"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsCategoryMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsDocMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsLinkMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryItemsMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsDocMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "document"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsLinkMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "href"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "category"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsCategoryMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsDocMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsLinkMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryItemsMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsDocMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "document"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsLinkMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "href"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsItemsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "category"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsCategoryMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsDocMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsLinkMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarItemsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsItemsMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SidebarMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "_warning"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SidebarItemsMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsLogoMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "alt"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "src"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarItemsItemsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pageLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "externalLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "position"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarItemsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pageLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "externalLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "position"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItemsItemsMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsNavbarMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "link"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pageLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "externalLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "position"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarItemsMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_docMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "docLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_pageMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pageLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsBlogMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsExternalMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "href"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "internal_doc"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_docMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "internal_page"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsInternal_pageMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blog"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsBlogMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "external"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsExternalMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterLinksMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "items"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksItemsMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsFooterMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "style"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "links"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterLinksMutation"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "copyright"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "SettingsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "_warning"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "logo"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsLogoMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tagline"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "navbar"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsNavbarMutation"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "footer"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SettingsFooterMutation"}}}]}]}}