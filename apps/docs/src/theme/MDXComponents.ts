import React from "react";
import MDXComponents from "@theme-original/MDXComponents";
import CodeBlock from "@theme-original/CodeBlock";
import Details from "@theme-original/Details";
import Tabs from "@theme-original/Tabs";
import TabItem from "@theme-original/TabItem";
import DocCardList from "@theme-original/DocCardList";

// import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
// import ParamsDetails from "@theme/ParamsDetails";
// import RequestSchema from "@theme/RequestSchema";
// import StatusCodes from "@theme/StatusCodes";
// import OperationTabs from "@theme/OperationTabs";
// import TabItem from "@theme/TabItem";
// import Heading from "@theme/Heading";

export default {
  ...MDXComponents,
  Details: Details,
  CodeBlock: CodeBlock,
  Tabs: Tabs,
  TabItem: TabItem,
  Admonition: MDXComponents.admonition,
  DocCardList: DocCardList,
};
